{"ast": null, "code": "import * as go from 'gojs';\nimport { LinkLabelDraggingTool } from 'src/app/extensions/LinkLabelDraggingTool';\nimport { linkPortList } from 'src/app/shared/configs/linkPortConfig';\nimport { paletteConfigs } from 'src/app/shared/configs/palletteConfigs';\nimport { AttributeType } from 'src/app/shared/model/attribute';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../access/access.service\";\nimport * as i2 from \"src/app/shared/utils/diagram-utils\";\nimport * as i3 from \"../property/property.service\";\nimport * as i4 from \"../diagram/event-listener.service\";\nimport * as i5 from \"./gojsClass/gojs-class.service\";\nimport * as i6 from \"./gojsEnumeration/gojs-enumeration.service\";\nimport * as i7 from \"./gojsLiteral/gojs-literal.service\";\nimport * as i8 from \"./gojsFolder/gojs-folder.service\";\nimport * as i9 from \"./gojsAttribute/gojs-attribute.service\";\nimport * as i10 from \"./gojsCardinality/gojs-cardinality.service\";\nimport * as i11 from \"./gojsCommon/gojs-common.service\";\nimport * as i12 from \"./gojs-comment/gojs-comment.service\";\nimport * as i13 from \"../treeNode/tree-node.service\";\nimport * as i14 from \"../snackbar/snack-bar.service\";\nimport * as i15 from \"../app.service\";\nexport class GojsService {\n  constructor(accessService, diagramUtils, propertyService, eventListenerService, goJsClassService, goJsEnumService, goJsLiteralService, goJsFolderService, goJsAttributeService, goJsCardinalityService, goJsCommonService, gojsCommentService, treeNodeService, snackBarService, appService) {\n    this.accessService = accessService;\n    this.diagramUtils = diagramUtils;\n    this.propertyService = propertyService;\n    this.eventListenerService = eventListenerService;\n    this.goJsClassService = goJsClassService;\n    this.goJsEnumService = goJsEnumService;\n    this.goJsLiteralService = goJsLiteralService;\n    this.goJsFolderService = goJsFolderService;\n    this.goJsAttributeService = goJsAttributeService;\n    this.goJsCardinalityService = goJsCardinalityService;\n    this.goJsCommonService = goJsCommonService;\n    this.gojsCommentService = gojsCommentService;\n    this.treeNodeService = treeNodeService;\n    this.snackBarService = snackBarService;\n    this.appService = appService;\n    this.$ = go.GraphObject.make;\n    this._hasEditAccessOnly = false;\n    this._attributeTypes = [];\n    this._diagrams = [];\n    /**\n     * Handles the completion of a drop operation in the diagram editor\n     * @param {go.InputEvent} event is related to drop orientation\n     * @param {*} objectData is dragged object data\n     * @memberof DiagramEditorComponent\n     */\n    this.handleDropCompletion = (event, objectData) => {\n      if (!this._hasEditAccessOnly || this._diagrams.length == 0) {\n        event.diagram.currentTool.doCancel();\n        return;\n      }\n      const selectedObjects = event.diagram.selection;\n      selectedObjects.each(obj => {\n        if (obj.data && obj.data.category === GojsNodeCategory.Association) {\n          return;\n        }\n        if (objectData && objectData.data.supportingLevels.includes(obj.data.category)) {\n          this.handleMemberDrop(obj, objectData, event);\n        } else if (obj.data.isGroup) {\n          this.handleTopLevelDrop(obj, event);\n        } else {\n          event.diagram.remove(obj);\n          return;\n        }\n      });\n    };\n    this.accessService.accessTypeChanges().subscribe(access => {\n      if (access != AccessType.Viewer) {\n        this._hasEditAccessOnly = true;\n      } else {\n        this._hasEditAccessOnly = false;\n      }\n    });\n    this.goJsCommonService.gojsDiagramChanges().subscribe(diagram => {\n      if (diagram) this._gojsDiagram = diagram;\n    });\n    this.diagramUtils.getAttributeTypes().subscribe(options => {\n      this._attributeTypes = options.sort((a, b) => a.name.localeCompare(b.name, undefined, {\n        sensitivity: 'base'\n      }));\n      if (this._gojsDiagram && this._gojsDiagram.model) {\n        this._gojsDiagram.model.commit(model => {\n          model.set(model.modelData, 'attributeTypes', options);\n        }, null);\n      }\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      this._diagrams = diagrams;\n      if (this._gojsDiagram && this._diagrams.length == 0) {\n        this._gojsDiagram.clear();\n        this.snackBarService.warn('snackBar.diagramDeleteInfo');\n      }\n      if (this._gojsDiagram) {\n        this._gojsDiagram.allowDrop = this._hasEditAccessOnly && this._diagrams.length > 0;\n      }\n    });\n  }\n  /**\n   *Load the go js component  with the selected diagram details and set it to the layout container.\n   * @private\n   * @memberof DiagramEditorComponent\n   */\n  initDiagram(diagram) {\n    this.goJsCommonService.setGojsDiagram(diagram);\n    this._gojsDiagram.isReadOnly = !this._hasEditAccessOnly;\n    const toolManager = this._gojsDiagram.toolManager;\n    if (this._hasEditAccessOnly) {\n      toolManager.mouseMoveTools.insertAt(0, new LinkLabelDraggingTool());\n    } else {\n      const firstTool = toolManager.mouseMoveTools.elt(0);\n      if (firstTool instanceof LinkLabelDraggingTool) {\n        toolManager.mouseMoveTools.removeAt(0);\n      }\n    }\n    if (this.appService.isInitProject()) {\n      this.initializeMainDiagram();\n      this.eventListenerService.addDiagramEventListeners();\n      diagram.div?.addEventListener('dragover', event => {\n        event.preventDefault();\n      });\n      diagram.div?.addEventListener('drop', event => {\n        this.onDropNode(event);\n      });\n    }\n  }\n  onDropNode(event) {\n    event.preventDefault();\n    const data = event.dataTransfer?.getData('text/plain');\n    if (data) {\n      const nodeData = JSON.parse(data);\n      if (nodeData.category === GojsNodeCategory.Diagram) {\n        event.stopPropagation();\n        return;\n      }\n      // Get the drop position in screen coordinates (clientX, clientY)\n      const clientX = event.clientX - 300;\n      const clientY = event.clientY - 50;\n      // Convert the screen coordinates to diagram coordinates\n      const dropPoint = this._gojsDiagram.transformViewToDoc(new go.Point(clientX, clientY));\n      if (nodeData.category === GojsNodeCategory.Class || nodeData.category === GojsNodeCategory.AssociativeClass || nodeData.category === GojsNodeCategory.Enumeration) {\n        if (this.goJsCommonService.checkGroupNodeExist(this._gojsDiagram, nodeData)) return;\n        delete nodeData.data.key;\n        delete nodeData.data.id;\n        this.handleGroupNodeCreationOrUpdate({\n          ...nodeData.data,\n          position: go.Point.stringify(dropPoint)\n        }, true);\n      } else if (nodeData.category === GojsNodeCategory.Folder) {\n        const spacing = 170;\n        const children = this.treeNodeService.getClassesEnumsFromFolder(nodeData);\n        const nonExistNode = [];\n        children.forEach((child, index) => {\n          const newPoint = new go.Point(dropPoint.x + index * spacing, dropPoint.y);\n          if (!this.goJsCommonService.checkGroupNodeExist(this._gojsDiagram, child)) {\n            this.handleGroupNodeCreationOrUpdate({\n              ...child.data,\n              position: go.Point.stringify(newPoint)\n            }, true);\n          } else {\n            nonExistNode.push(child);\n          }\n        });\n        this.goJsCommonService.selectMultipleGroupNodeExist(this._gojsDiagram, nonExistNode);\n      }\n    }\n  }\n  initPaletteDiagram() {\n    const componentNodeTemplate = this.getComponentNodeTemplate();\n    const componentGroupTemplate = this.getComponentGroupTemplate();\n    this.configureComponentPalette(componentNodeTemplate, componentGroupTemplate);\n  }\n  /**\n   * Initializes the main diagram with configuration settings and tools.\n   * This method sets up the diagram's properties, tools, templates, and event listeners\n   * templates, and event listeners.\n   * @memberof DiagramEditorComponent\n   */\n  initializeMainDiagram() {\n    this.setupDiagramTools();\n    this.initializeItemTemplate();\n    this.configureGroupTemplate();\n    this.configureLinkTemplate();\n    this.configureGroupTemplateAdornment();\n    this.setupDiagramModel();\n  }\n  /**\n   * Sets up tools and context menus for the diagram.\n   * @private\n   * @memberof DiagramEditorComponent\n   */\n  setupDiagramTools() {\n    if (this._gojsDiagram && this._gojsDiagram.toolManager && this._gojsDiagram.toolManager.panningTool) {\n      this._gojsDiagram.toolManager.panningTool.isEnabled = this._hasEditAccessOnly;\n    }\n  }\n  /**\n   * Initializes and returns the item template for the diagram.\n   * This template includes text blocks for name, category, and type with appropriate bindings and styles.\n   *\n   * @private\n   * @returns {go.Panel} The configured item template panel.\n   * @memberof DiagramEditorComponent\n   */\n  initializeItemTemplate() {\n    return this.$(go.Panel, 'Horizontal', {\n      alignment: go.Spot.TopLeft,\n      stretch: go.Stretch.Fill\n    }, this.createNameTextBlock(), this.createCategoryTextBlock(), this.createTypeTextBlock());\n  }\n  /**\n   * Creates and returns a text block for the name with bindings and styles.\n   *\n   * @private\n   * @returns {go.TextBlock} The configured name text block.\n   * @memberof DiagramEditorComponent\n   */\n  createNameTextBlock() {\n    return this.$(go.TextBlock, {\n      isMultiline: false,\n      alignment: go.Spot.Center,\n      minSize: new go.Size(50, NaN),\n      margin: new go.Margin(3, 10, 0, 10),\n      overflow: go.TextOverflow.Ellipsis,\n      wrap: go.Wrap.Fit,\n      textEdited: (textBlock, oldText, newText) => {\n        if (!newText.trim()) {\n          // If the new text is empty or contains only spaces, restore the old value\n          textBlock.text = oldText;\n        }\n      }\n    }, new go.Binding('text', 'name').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay());\n  }\n  /**\n   * Creates and returns a text block for the category with bindings and styles.\n   * @private\n   * @returns {go.TextBlock} The configured category text block.\n   * @memberof DiagramEditorComponent\n   */\n  createCategoryTextBlock() {\n    return this.$(go.TextBlock, ':', {\n      stroke: 'black'\n    }, new go.Binding('visible', 'category', category => {\n      return category === GojsNodeCategory.Attribute || category === GojsNodeCategory.Operation;\n    }));\n  }\n  /**\n   * Creates and returns a text block for the type with bindings, styles, and choices.\n   *\n   * @private\n   * @returns {go.TextBlock} The configured type text block.\n   * @memberof DiagramEditorComponent\n   */\n  createTypeTextBlock() {\n    return this.$(go.TextBlock, {\n      isMultiline: false,\n      alignment: go.Spot.Center,\n      margin: new go.Margin(3, 10, 0, 10),\n      textEditor: window.TextEditorSelectBox\n    }, new go.Binding('choices', '', () => {\n      return this._attributeTypes.sort();\n    }).makeTwoWay(), new go.Binding('text', 'dataType', dataType => {\n      const attributeOption = this._attributeTypes.find(option => option.id == dataType || option.name == dataType);\n      return attributeOption ? attributeOption.name : `${AttributeType[AttributeType.Undefined]}`;\n    }).makeTwoWay(), new go.Binding('name', 'dataType', dataType => {\n      const attributeOption = this._attributeTypes.find(option => option.id == dataType || option.name == dataType);\n      return attributeOption ? attributeOption.id : `0_${AttributeType[AttributeType.Undefined]}`;\n    }).makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay(), new go.Binding('visible', 'category', category => {\n      return category === GojsNodeCategory.Attribute || category === GojsNodeCategory.Operation;\n    }));\n  }\n  /**\n   * Configures the group template for the diagram with specific behaviors and styles.\n   * This method orchestrates the creation of group templates and applies them to the diagram.\n   *\n   * @private\n   * @memberof DiagramEditorComponent\n   */\n  configureGroupTemplate() {\n    this._gojsDiagram.groupTemplate = this.createGroupTemplate();\n    this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.Package, this.createPackageGroupTemplate());\n    this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.Enumeration, this.createEnumerationGroupTemplate());\n    this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.Comment, this.createCommentGroupTemplate());\n    this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.AssociativeClass, this.createAssociativeClassGroupTemplate());\n    this._gojsDiagram.nodeTemplateMap.add(GojsNodeCategory.LinkLabel, this.createLinkLabelTemplate());\n  }\n  /**\n   * Creates and returns the main group template for the diagram.\n   *\n   * @private\n   * @returns {go.Group} The configured group template.\n   * @memberof DiagramEditorComponent\n   */\n  createGroupTemplate() {\n    return this.$(go.Group, 'Auto', {\n      ...this.getClassOrAssociativeProperties()\n    },\n    //Binding the common property\n    ...this.getCommonBindings(), this.createGroupShape(), this.createGroupPanels(), ...linkPortList.map(linkPort => this.createPort(linkPort.portId, linkPort.alignment, linkPort.isFromLinkable, linkPort.isToLinkable)));\n  }\n  /**\n   * Creates or updates a class on the diagram.\n   *\n   * @param classData - The data of the class to be created or updated.\n   * @param event - The GoJS input event.\n   */\n  handleGroupNodeCreationOrUpdate(groupNodeData, isFromLibrary, event) {\n    if (groupNodeData['category'] === GojsNodeCategory.Class || groupNodeData['category'] === GojsNodeCategory.AssociativeClass) {\n      this.goJsClassService.handleClassCreationOrUpdate(groupNodeData, this._gojsDiagram, isFromLibrary, event);\n    } else if (groupNodeData['category'] === GojsNodeCategory.Enumeration) {\n      this.goJsEnumService.handleEnumCreationOrUpdate(groupNodeData, this._gojsDiagram, isFromLibrary, event);\n    }\n  }\n  /**\n   * Handles top-level drops in the diagram.\n   *\n   * @param obj - The dropped object.\n   * @param event - The GoJS input event.\n   */\n  handleTopLevelDrop(obj, event) {\n    if (obj.data.allowTopLevelDrops === true) {\n      event.diagram.commandHandler.addTopLevelParts(event.diagram.selection, true);\n      if (obj.data.category === GojsNodeCategory.Class || obj.data.category === GojsNodeCategory.Enumeration || obj.data.category === GojsNodeCategory.AssociativeClass) {\n        this.handleGroupNodeCreationOrUpdate(obj.data, false, event);\n      } else if (obj.data.category === GojsNodeCategory.Comment) {\n        this.gojsCommentService.handleCommentDrop(obj.data, this._gojsDiagram);\n      }\n      event.diagram.clearSelection();\n    } else {\n      event.diagram.currentTool.doCancel();\n    }\n  }\n  /**\n   * Handles member element drops in the diagram.\n   * @param obj - The dropped object.\n   * @param objectData - The target object data.\n   * @param event - The GoJS input event.\n   */\n  handleMemberDrop(obj, objectData, event) {\n    if (objectData.data.supportingLevels.includes(obj.category) && !event.diagram.toolManager.textEditingTool.isActive) {\n      if (obj.data.category === GojsNodeCategory.Attribute || obj.data.category === GojsNodeCategory.Operation) {\n        this.goJsAttributeService.handleAttributeDrop(obj, objectData.data, event, this._gojsDiagram, this._hasEditAccessOnly);\n        this.handleSelectionDeleting(objectData);\n      } else if (obj.data.category === GojsNodeCategory.Class) {\n        this.handleGroupNodeCreationOrUpdate(obj.data, false, event);\n        objectData.addMembers(objectData.diagram.selection, true);\n      } else if (obj.data.category === GojsNodeCategory.EnumerationLiteral) {\n        this.goJsLiteralService.handleLiteralDrop(obj, objectData.data, event, this._gojsDiagram, this._hasEditAccessOnly);\n        this.handleSelectionDeleting(objectData);\n      }\n    } else {\n      const diagram = event.diagram;\n      // Remove the dropped node from the diagram\n      if (diagram && obj) {\n        diagram.remove(obj);\n      }\n      // Cancel the operation and clear selection\n      event.diagram.currentTool.doCancel();\n    }\n  }\n  /**\n   * Handles the selection deleting event for the diagram.\n   *\n   * @param objectData - The target object data.\n   */\n  handleSelectionDeleting(objectData) {\n    this._gojsDiagram.removeDiagramListener('SelectionDeleting', this.eventListenerService.addDeletingEventListener);\n    objectData.diagram.commandHandler.deleteSelection();\n    this._gojsDiagram.addDiagramListener('SelectionDeleting', this.eventListenerService.addDeletingEventListener);\n    this._gojsDiagram.model.updateTargetBindings(objectData);\n  }\n  /**\n   * Handles mouse drag enter events for groups.\n   *\n   * @private\n   * @param {go.DiagramEvent} _e - The diagram event.\n   * @param {go.GraphObject} grp - The group being entered.\n   * @param {go.GraphObject} _prev - The previous object.\n   */\n  handleMouseDragEnter(_e, grp, _prev) {\n    this.highlightGroup(grp, true);\n  }\n  createNewFolder(name, projectId) {\n    this.goJsFolderService.onCreateNewFolder(name, projectId, this._hasEditAccessOnly);\n  }\n  /**\n   * Handles mouse drag leave events for groups.\n   *\n   * @private\n   * @param {go.DiagramEvent} _e - The diagram event.\n   * @param {go.GraphObject} grp - The group being left.\n   * @param {go.GraphObject} _next - The next object.\n   */\n  handleMouseDragLeave(_e, grp, _next) {\n    this.highlightGroup(grp, false);\n  }\n  /**\n   * Handles selection changes for groups.\n   *\n   * @private\n   * @param {go.Part} node - The node whose selection changed.\n   */\n  handleSelectionChanged(node) {\n    this.propertyService.transferDataOnSelection(node);\n  }\n  /**\n   * Group shape color is highlighted or not\n   * @param {*} grp is used  to identify whether the element belongs to a group or\n   * @param {boolean} show is used for group visible or not\n   * @return {boolean}\n   * @memberof DiagramEditorComponent\n   */\n  highlightGroup(grp, show) {\n    if (!grp) return false;\n    // check that the drop may really happen into the Group\n    const tool = grp.diagram.toolManager.draggingTool;\n    grp.isHighlighted = show && grp.canAddMembers(tool.draggingParts);\n    return grp.isHighlighted;\n  }\n  /**\n   * Toggles the visibility of small ports on a node in the GoJS diagram.\n   *\n   * @param event - The input event that triggers the visibility change.\n   * @param node - The node whose ports will be shown or hidden.\n   * @param show - A boolean indicating whether to show or hide the ports.\n   */\n  toggleSmallPortsVisibility(node, show) {\n    node.ports.each(port => {\n      if (port.portId !== '') {\n        port.fill = show ? 'rgba(0, 0, 0, 0.3)' : null;\n      }\n    });\n    if (this._gojsDiagram && this._gojsDiagram.toolManager.linkingTool) {\n      this._gojsDiagram.toolManager.linkingTool.archetypeLinkData = {\n        category: node.data.category == GojsNodeCategory.AssociativeClass ? GojsNodeCategory.LinkToLink : GojsNodeCategory.Association\n      };\n    }\n  }\n  /**\n   * Creates the visual shape for groups.\n   * @private\n   * @returns {go.Shape} The shape configuration.\n   */\n  createGroupShape(additionalProperties = {}, isAssociative = false) {\n    const bindingProps = [new go.Binding('visible', 'showTablePanel').makeTwoWay(), new go.Binding('fromLinkable', 'editable').makeTwoWay(), new go.Binding('toLinkable', 'editable').makeTwoWay(), new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(), new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(), new go.Binding('fill', 'color', value => {\n      return this.$(go.Brush, 'Linear', {\n        0.0: this.goJsCommonService.updateRGBAColorWithOpacity(value, 0.4),\n        1.0: value\n      });\n    })];\n    if (!isAssociative) {\n      bindingProps.push(new go.Binding('stroke', 'color').makeTwoWay());\n    }\n    return this.$(go.Shape, 'RoundedRectangle', {\n      cursor: 'pointer',\n      ...additionalProperties\n    }, ...bindingProps);\n  }\n  /**\n   * Creates and returns the group panel with the specified properties and bindings.\n   * @returns {go.Panel} The group panel.\n   */\n  createGroupPanels() {\n    return this.$(go.Panel, 'Vertical', this.createTablePanel());\n  }\n  /**\n   * Creates and returns the table panel containing the components of the group panel.\n   * @returns {go.Panel} The table panel.\n   */\n  createTablePanel() {\n    return this.$(go.Panel, 'Table', {\n      name: 'Shape',\n      defaultRowSeparatorStroke: 'black',\n      defaultRowSeparatorStrokeWidth: 1,\n      portId: '',\n      cursor: 'pointer',\n      minSize: new go.Size(150, 100)\n    }, this.createRowColumnDefinitions(), this.createTitleTextBlock(), this.createPropertiesPanel(), this.createMethodsPanel(), new go.Binding('visible', 'showTablePanel').makeTwoWay(), new go.Binding('desiredSize', 'size').makeTwoWay());\n  }\n  /**\n   * Creates and returns the row and column definitions for the table panel.\n   * @returns {go.RowColumnDefinition[]} The row and column definitions.\n   */\n  createRowColumnDefinitions() {\n    return [this.$(go.RowColumnDefinition, {\n      row: 0,\n      minimum: 25,\n      maximum: 25,\n      stretch: go.Stretch.Fill,\n      separatorStrokeWidth: 1,\n      separatorStroke: 'black'\n    }), this.$(go.RowColumnDefinition, {\n      row: 1,\n      minimum: 60,\n      stretch: go.Stretch.Fill,\n      position: 20\n    })];\n  }\n  /**\n   * Creates and returns the title TextBlock for the table panel.\n   * @returns {go.TextBlock} The title TextBlock.\n   */\n  createTitleTextBlock() {\n    return this.$(go.TextBlock, {\n      row: 0,\n      columnSpan: 2,\n      font: 'bold 12pt sans-serif',\n      minSize: new go.Size(150, NaN),\n      margin: new go.Margin(0, 4, 0, 4),\n      isMultiline: false,\n      wrap: go.Wrap.Fit,\n      alignment: go.Spot.Center,\n      alignmentFocus: go.Spot.Center,\n      editable: true,\n      textAlign: 'center',\n      name: 'TEXTBLOCK',\n      textEdited: this.handleTableTextEdited.bind(this)\n    }, new go.Binding('text', 'name').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay());\n  }\n  /**\n   * Handles the text edited event to update the property data.\n   * @param {go.TextBlock} textBlock - The TextBlock that was edited.\n   * @param {string} oldString - The old text string.\n   * @param {string} newString - The new text string.\n   */\n  handleTableTextEdited(textBlock, oldString, newString) {\n    if (!newString.trim()) {\n      // If the new text is empty or contains only spaces, restore the old value\n      textBlock.text = oldString;\n    } else {\n      const tableData = textBlock.part?.data;\n      if (tableData.category === GojsNodeCategory.Comment) {\n        this.gojsCommentService.updateComment(tableData.name, newString, tableData, this._gojsDiagram);\n      } else {\n        this.propertyService.transferDataOnSelection(textBlock.part);\n      }\n      if (tableData.category === GojsNodeCategory.Class || tableData.category === GojsNodeCategory.AssociativeClass) {\n        this.goJsClassService.updateTemplateClass({\n          ...tableData,\n          name: newString\n        }, this._gojsDiagram);\n      }\n      if (tableData.category === GojsNodeCategory.Enumeration) {\n        this.goJsEnumService.updateEnumerationFromDiagram({\n          ...tableData,\n          name: newString\n        }, this._gojsDiagram);\n      }\n    }\n  }\n  /**\n   * Creates and returns the properties panel for the table panel.\n   * @returns {go.Panel} The properties panel.\n   */\n  createPropertiesPanel() {\n    return this.$(go.Panel, 'Vertical', {\n      name: 'PROPERTIES',\n      row: 1,\n      stretch: go.Stretch.Fill,\n      alignment: go.Spot.Center,\n      itemTemplate: this.initializeItemTemplate()\n    }, new go.Binding('itemArray', 'items', items => items.filter(item => item.category == GojsNodeCategory.EnumerationLiteral || item.category == GojsNodeCategory.Attribute)), new go.Binding('visible', 'items', items => items.some(item => item.category === GojsNodeCategory.Attribute || item.category === GojsNodeCategory.EnumerationLiteral)));\n  }\n  /**\n   * Creates and returns the methods panel for the table panel.\n   * @returns {go.Panel} The methods panel.\n   */\n  createMethodsPanel() {\n    return this.$(go.Panel, 'Vertical', {\n      name: 'Methods',\n      row: 2,\n      stretch: go.Stretch.Fill,\n      alignment: go.Spot.Center,\n      itemTemplate: this.initializeItemTemplate()\n    }, new go.Binding('itemArray', 'items', items => items.filter(item => item.category == GojsNodeCategory.Operation)), new go.Binding('visible', 'items', items => items.some(item => item.category === GojsNodeCategory.Operation)));\n  }\n  /**\n   * Creates a port shape for a node in the GoJS diagram.\n   *\n   * @param portId - The unique identifier for the port.\n   * @param alignment - The alignment spot for the port on the node.\n   * @param isOutput - Specifies if the port is for outgoing links.\n   * @param isInput - Specifies if the port is for incoming links.\n   * @returns A GoJS Shape configured as a port.\n   */\n  createPort(portId, alignment, isOutput, isInput) {\n    return this.$(go.Shape, {\n      figure: 'Circle',\n      fill: 'transparent',\n      stroke: null,\n      desiredSize: new go.Size(9, 9),\n      alignment: alignment,\n      alignmentFocus: alignment,\n      portId: portId,\n      fromSpot: alignment,\n      toSpot: alignment,\n      fromLinkable: isOutput,\n      toLinkable: isInput,\n      fromLinkableSelfNode: isOutput,\n      toLinkableSelfNode: isInput,\n      cursor: 'pointer'\n    });\n  }\n  /**\n   * Creates a specialized group template for packages.\n   *\n   * @private\n   * @returns {go.Group} The package group template.\n   */\n  createPackageGroupTemplate() {\n    return this.$(go.Group, 'Auto', {\n      background: 'blue',\n      ungroupable: true,\n      mouseDragEnter: (_e, grp) => this.highlightGroup(grp, true),\n      mouseDragLeave: (_e, grp) => this.highlightGroup(grp, false),\n      computesBoundsAfterDrag: true,\n      computesBoundsIncludingLocation: true,\n      mouseDrop: this.handleDropCompletion,\n      handlesDragDropForMembers: true,\n      resizable: true,\n      resizeObjectName: 'Placeholder'\n    }, this.createPackageGroupShape(), this.createPackageGroupPanels());\n  }\n  /**\n   * Creates the visual shape for package groups.\n   *\n   * @private\n   * @returns {go.Shape} The shape configuration.\n   */\n  createPackageGroupShape() {\n    return this.$(go.Shape, 'RoundedRectangle', {\n      stroke: this.defaultColor(true),\n      fill: this.defaultColor(true),\n      strokeWidth: 2\n    }, new go.Binding('stroke', 'horiz', this.defaultColor), new go.Binding('fill', 'horiz', this.defaultColor));\n  }\n  /**\n   * Creates panels for package groups.\n   *\n   * @private\n   * @returns {go.Panel} The panel configuration.\n   */\n  createPackageGroupPanels() {\n    return this.$(go.Panel, 'Vertical', {\n      name: 'Placeholder'\n    }, this.createPackageGroupHeader(), this.$(go.Placeholder, {\n      padding: 5,\n      alignment: go.Spot.LeftCenter,\n      minSize: new go.Size(200, 150)\n    }));\n  }\n  createCommentGroupPanels() {\n    return this.$(go.Panel, 'Vertical', {\n      name: 'Placeholder'\n    }, this.createPackageGroupHeader(), this.$(go.TextBlock, {\n      font: 'bold 10pt Helvetica, Arial, sans-serif',\n      margin: new go.Margin(10, 8, 4, 8),\n      textAlign: 'left',\n      maxLines: Infinity,\n      minSize: new go.Size(NaN, 200),\n      wrap: go.Wrap.Fit,\n      // isMultiline: false,\n      alignment: go.Spot.TopLeft,\n      overflow: go.TextOverflow.Clip,\n      stretch: go.Stretch.Fill,\n      textEdited: this.handleTableTextEdited.bind(this),\n      mouseDrop: (e, _obj) => e.diagram.currentTool.doCancel()\n    }, new go.Binding('text', 'description').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay(), new go.Binding('desiredSize', 'size').makeTwoWay()));\n  }\n  /**\n   * Creates the header panel for package groups.\n   *\n   * @private\n   * @returns {go.Panel} The header panel configuration.\n   */\n  createPackageGroupHeader() {\n    return this.$(go.Panel, 'Table', {\n      stretch: go.Stretch.Horizontal,\n      background: this.defaultColor(true)\n    }, new go.Binding('background', 'horiz', this.defaultColor), this.$(go.TextBlock, {\n      alignment: go.Spot.Left,\n      stretch: go.Stretch.Horizontal,\n      editable: true,\n      isMultiline: false,\n      wrap: go.Wrap.Fit,\n      margin: 5,\n      font: this.defaultFont(false),\n      opacity: 0.95,\n      stroke: '#404040',\n      textEdited: this.handleTextEdited.bind(this)\n    }, new go.Binding('font', 'horiz', this.defaultFont), new go.Binding('text', 'name').makeTwoWay()));\n  }\n  handleTextEdited(textBlock, _oldString, newString) {\n    const tableData = textBlock.diagram?.selection.first()?.data;\n    if (tableData.category === GojsNodeCategory.Comment) {\n      this.gojsCommentService.updateComment(newString, tableData.description, tableData, this._gojsDiagram);\n    }\n  }\n  /**\n   *For getting the default color\n   * @private\n   * @param {boolean} horiz is boolean  value for horizontal or vertical layout\n   * @return {*}  {string}\n   * @memberof DiagramEditorComponent\n   */\n  defaultColor(horiz) {\n    // a Binding conversion function\n    return horiz ? 'rgba(255, 221, 51, 0.55)' : 'rgba(51,211,229, 0.5)';\n  }\n  /**\n   *For getting the default font\n   * @param {boolean} horiz is boolean  value for horizontal or vertical layout\n   * @return {*}  {string}\n   * @memberof DiagramEditorComponent\n   */\n  defaultFont(horiz) {\n    // a Binding conversion function\n    return horiz ? 'bold 20px sans-serif' : 'bold 16px sans-serif';\n  }\n  createEnumerationGroupTemplate() {\n    return this.$(go.Group, 'Auto', {\n      selectionAdornmentTemplate: this.$(go.Adornment, 'Spot', this.createSelectionBorderPanel(), this.createActionButtonPanel(false)),\n      ...this.getCommonGroupProperties()\n    },\n    //Binding the common property\n    ...this.getCommonBindings(), this.createGroupShape(), this.createGroupPanels());\n  }\n  /**\n   * Creates a button for adding attributes to the group node.\n   *\n   * @returns {go.Panel} The configured attribute button panel.\n   */\n  createAttributeButton(isForLiteral) {\n    return this.createActionButton(!isForLiteral ? GoJsNodeIcon.Attribute : GoJsNodeIcon.EnumerationLiteral, 'Attribute', (_e, obj) => isForLiteral ? this.addNodeLiteral(obj) : this.goJsAttributeService.addNodeAttribute(obj, 'Attribute', GojsNodeCategory.Attribute, this._gojsDiagram));\n  }\n  /**\n   * Creates a button with an image and a specified click handler.\n   * This method helps in reducing redundancy by allowing the creation of multiple buttons with similar structure.\n   *\n   * @param {string} imagePath - The path to the button image.\n   * @param {Function} clickHandler - The function to be executed on button click.\n   * @returns {go.Panel} The configured button panel.\n   */\n  createActionButton(iconUnicode,\n  // Accepts Font Awesome Unicode (e.g., '\\uf1fe')\n  tooltipText,\n  // Tooltip text to display on hover\n  clickHandler) {\n    return this.$('Button', {\n      click: clickHandler,\n      margin: new go.Margin(0, 10, 0, 0),\n      toolTip: this.$(go.Adornment,\n      // Tooltip container\n      'Auto',\n      // Auto layout for proper sizing\n      {\n        alignment: go.Spot.Top,\n        alignmentFocus: go.Spot.Bottom // Tooltip bottom edge aligns with button's top edge\n      }, this.$(go.Shape, {\n        fill: 'white',\n        stroke: null\n      } // Tooltip background\n      ), this.$(go.TextBlock, {\n        font: '10pt sans-serif',\n        margin: 5,\n        textAlign: 'center',\n        wrap: go.Wrap.Fit,\n        alignment: go.Spot.Top,\n        alignmentFocus: go.Spot.Top // Tooltip bottom edge aligns with button's top edge\n      }, tooltipText // Tooltip text content\n      ))\n    }, this.$(go.TextBlock,\n    // Use a TextBlock for Font Awesome icons\n    {\n      font: '14px FontAwesome',\n      text: iconUnicode,\n      textAlign: 'center',\n      verticalAlignment: go.Spot.Center,\n      desiredSize: new go.Size(15, 15) // Adjust size as needed\n    }));\n  }\n  /**\n   * Adds a literal node to the currently selected node in the GoJS diagram.\n   *\n   * This method retrieves the currently selected node in the diagram and uses\n   * the `GoJsLiteralService` to create a new literal node associated with the selected node.\n   * The `idTemplateEnumeration` is passed to help identify the template enumeration.\n   *\n   * @param obj - The GoJS `GraphObject` from which the selected node is retrieved.\n   */\n  addNodeLiteral(obj) {\n    // Get the currently selected node in the diagram\n    const selectedNode = obj.diagram.selection.first();\n    // Extract the `idTemplateEnumeration` from the selected node's data\n    const idTemplateEnumeration = selectedNode.data.idTemplateEnumeration;\n    // Use the GoJsLiteralService to create a literal node\n    this.goJsLiteralService.onCreateLiteral(selectedNode.data, 'Literal', idTemplateEnumeration, this._gojsDiagram);\n  }\n  /**\n   * Configures the link template for the diagram with specific behaviors and styles.\n   * This method orchestrates the creation of link templates and applies them to the diagram.\n   *\n   * @private\n   * @memberof DiagramEditorComponent\n   */\n  configureLinkTemplate() {\n    this._gojsDiagram.linkTemplate = this.createLinkTemplate();\n    this._gojsDiagram.linkTemplateMap.add(GojsNodeCategory.LinkToLink, this.createLinkToLinkTemplate());\n  }\n  /**\n   * Creates and returns the link template for the diagram.\n   *\n   * @private\n   * @returns {go.Link} The configured link template.\n   * @memberof DiagramEditorComponent\n   */\n  createLinkTemplate() {\n    return this.$(go.Link, {\n      selectionChanged: this.handleSelectionChanged.bind(this)\n    }, this.customizeLinkTemplate(), new go.Binding('selectable', 'editable').makeTwoWay(), new go.Binding('reshapable', 'editable').makeTwoWay(), new go.Binding('resegmentable', 'editable').makeTwoWay(), new go.Binding('relinkableFrom', 'editable').makeTwoWay(), new go.Binding('relinkableTo', 'editable').makeTwoWay(), new go.Binding('deletable', 'editable').makeTwoWay(), new go.Binding('toLinkableDuplicates', 'editable').makeTwoWay(),\n    // Instead of 'editable', you might use the following bindings:\n    new go.Binding('fromLinkable', 'editable').makeTwoWay(), new go.Binding('toLinkable', 'editable').makeTwoWay(), new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(), new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(), this.createLinkShape(), this.createLinkNameShape('name'), this.createCardinalityTextBlock('Cardinality1', 0, 0.5, new go.Point(15, -15), 'cardinalityTo'), this.createCardinalityTextBlock('Cardinality2', -1, 0.5, new go.Point(-15, -15), 'cardinalityFrom'), this.$(go.TextBlock, {\n      // text: 'fromComment',\n      segmentIndex: 0,\n      segmentOffset: new go.Point(NaN, 15),\n      editable: true,\n      maxSize: new go.Size(200, NaN),\n      overflow: go.TextOverflow.Ellipsis,\n      wrap: go.Wrap.Fit,\n      isMultiline: false\n    }, new go.Binding('text', 'fromComment').makeTwoWay()), this.$(go.TextBlock, {\n      // text: 'toComment',\n      segmentIndex: -1,\n      segmentOffset: new go.Point(NaN, 15),\n      editable: true,\n      maxSize: new go.Size(200, NaN),\n      overflow: go.TextOverflow.Ellipsis,\n      wrap: go.Wrap.Fit,\n      isMultiline: false\n    }, new go.Binding('text', 'toComment').makeTwoWay()));\n  }\n  createLinkToLinkTemplate() {\n    return this.$(go.Link, {\n      deletable: false,\n      movable: false\n    }, new go.Binding('relinkableFrom', 'editable'), new go.Binding('relinkableTo', 'editable'), new go.Binding('selectable', 'editable'), new go.Binding('reshapable', 'editable'), this.$(go.Shape, {\n      fill: 'black',\n      strokeWidth: 2,\n      strokeDashArray: [4, 8]\n    }));\n  }\n  createLinkNameShape(bindingProp) {\n    return this.$(go.Panel, 'Auto', this.$(go.Shape, {\n      fill: this.$(go.Brush, 'Radial'),\n      stroke: 'transparent',\n      background: 'white'\n    }), this.$(go.TextBlock, {\n      textAlign: 'center',\n      font: '10pt helvetica, arial, sans-serif',\n      stroke: 'black',\n      margin: 4,\n      isMultiline: false,\n      editable: true\n    }, new go.Binding('text', bindingProp).makeTwoWay(), new go.Binding('segmentFraction', 'labelPosition.segmentFraction').makeTwoWay()), new go.Binding('segmentOffset', 'segmentOffset', go.Point.parse).makeTwoWay(go.Point.stringify));\n  }\n  /**\n   * For customize the link shape and style in pallette\n   * @private\n   * @returns {go.Link}\n   * @memberOf DiagramEditorComponent\n   */\n  customizeLinkTemplate() {\n    return {\n      routing: go.Routing.AvoidsNodes,\n      corner: 5,\n      fromEndSegmentLength: 30,\n      toEndSegmentLength: 30,\n      curve: go.Curve.JumpOver,\n      toShortLength: 4\n    };\n  }\n  /**\n   * Creates a shape for the link.\n   * @private\n   * @returns {go.Shape} The shape configuration.\n   */\n  createLinkShape() {\n    return this.$(go.Shape, {\n      segmentFraction: 10,\n      width: 100,\n      strokeWidth: 1.2 // Add a slightly thicker stroke for better visibility\n    },\n    // Add color bindings\n    new go.Binding('stroke', 'color').makeTwoWay(), new go.Binding('strokeWidth', 'isHighlighted', h => h ? 2.5 : 1.5).ofObject());\n  }\n  /**\n   * Creates a text block for cardinality with specified bindings, choices, and styles.\n   * @private\n   * @param {string} name - The name of the text block.\n   * @param {number} segmentIndex - The segment index.\n   * @param {number} segmentFraction - The segment fraction.\n   * @param {go.Point} segmentOffset - The segment offset.\n   * @param {string} cardinalityText - The cardinality name\n   * @returns {go.TextBlock} The configured cardinality text block.\n   */\n  createCardinalityTextBlock(name, segmentIndex, segmentFraction, segmentOffset, cardinalityText) {\n    return this.$(go.TextBlock, {\n      name: name,\n      segmentIndex: segmentIndex,\n      segmentFraction: segmentFraction,\n      segmentOffset: segmentOffset,\n      textEditor: window.TextEditorSelectBox,\n      choices: ['0..1', '1', '*', '1..*']\n    }, new go.Binding('text', cardinalityText).makeTwoWay(), new go.Binding('choices', 'choices').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay());\n  }\n  /**\n   * Configures the selection adornment template for group nodes in the diagram.\n   * The adornment includes a border and buttons for adding attributes, methods, and drawing links.\n   *\n   * @private\n   *\n   * @memberOf DiagramEditorComponent\n   */\n  configureGroupTemplateAdornment() {\n    this._gojsDiagram.groupTemplate.selectionAdornmentTemplate = this.$(go.Adornment, 'Spot', this.createSelectionBorderPanel(), this.createActionButtonPanel(true, false));\n  }\n  /**\n   * Creates an Auto Panel that serves as the background for the adornment,\n   * with a border to highlight the selected group node.\n   * @private\n   *\n   * @returns {go.Panel} The configured Auto Panel.\n   * @memberOf DiagramEditorComponent\n   */\n  createSelectionBorderPanel() {\n    return this.$(go.Panel, 'Auto', this.$(go.Shape, {\n      stroke: 'dodgerblue',\n      strokeWidth: 2,\n      fill: null\n    }), this.$(go.Placeholder));\n  }\n  /**\n   * Creates a Horizontal Panel that contains buttons for adding attributes, methods, and drawing links.\n   * The panel is aligned at the top of the group node and positioned just below it.\n   *\n   * @returns {go.Panel} The configured Horizontal Panel.\n   */\n  createActionButtonPanel(isClass, isAssociative = false) {\n    const actionPanels = [];\n    if (isClass) {\n      actionPanels.push(this.createAttributeButton(false));\n      actionPanels.push(this.createMethodButton());\n      if (!isAssociative) actionPanels.push(this.createLinkButton());\n    } else {\n      actionPanels.push(this.createAttributeButton(true));\n    }\n    return this.$(go.Panel, 'Horizontal', {\n      alignment: go.Spot.Top,\n      alignmentFocus: go.Spot.Bottom\n    }, ...actionPanels);\n  }\n  /**\n   * Creates a button for adding methods to the group node.\n   * @returns {go.Panel} The configured method button panel.\n   */\n  createMethodButton() {\n    return this.createActionButton(GoJsNodeIcon.Operation, 'Method', (_e, obj) => this.goJsAttributeService.addNodeAttribute(obj, 'Method', GojsNodeCategory.Operation, this._gojsDiagram));\n  }\n  /**\n   * Creates a button for drawing links between nodes.\n   * The button supports both click and drag actions to initiate a link drawing operation.\n   * @returns {go.Panel} The configured link button panel.\n   */\n  createLinkButton() {\n    return this.$('Button', {\n      click: (e, obj) => this.initiateLinkDrawing(e, obj),\n      actionMove: (e, obj) => this.initiateLinkDrawing(e, obj),\n      toolTip: this.$(go.Adornment,\n      // Tooltip container\n      'Auto',\n      // Auto layout for proper sizing\n      {\n        alignment: go.Spot.Top,\n        alignmentFocus: go.Spot.Bottom // Tooltip bottom edge aligns with button's top edge\n      }, this.$(go.Shape, {\n        fill: 'white',\n        stroke: null\n      } // Tooltip background\n      ), this.$(go.TextBlock, {\n        font: '10pt sans-serif',\n        margin: 5,\n        textAlign: 'center',\n        wrap: go.Wrap.Fit,\n        alignment: go.Spot.Top,\n        alignmentFocus: go.Spot.Top // Tooltip bottom edge aligns with button's top edge\n      }, 'Draw Link' // Tooltip text content\n      ))\n    }, this.$(go.Shape, {\n      geometryString: 'M0 0 L8 0 8 12 14 12 M12 10 L14 12 12 14',\n      desiredSize: new go.Size(15, 15),\n      fill: 'lightyellow'\n    }));\n  }\n  /**\n   * Initiates the linking tool to draw a link from the selected node.\n   * @param {go.InputEvent} event - The input event triggering the link creation.\n   * @param {go.GraphObject} button - The button that was clicked to start drawing the link.\n   */\n  initiateLinkDrawing(event, button) {\n    const selectedNode = button.part.adornedPart;\n    const linkingTool = event.diagram.toolManager.linkingTool;\n    const specificPort = selectedNode.findPort('R2');\n    if (specificPort) {\n      linkingTool.startObject = specificPort;\n      event.diagram.currentTool = linkingTool;\n      linkingTool.doActivate();\n    } else {\n      console.error('Port not found on the selected node');\n    }\n  }\n  /**\n   * Sets up the model for the diagram\n   *\n   * @private\n   * @memberof DiagramEditorComponent\n   */\n  setupDiagramModel() {\n    this.diagramUtils.initializeDiagramModelData(this._gojsDiagram);\n  }\n  createCommentGroupTemplate() {\n    return this.$(go.Group, 'Auto', {\n      background: 'blue',\n      ungroupable: true,\n      computesBoundsAfterDrag: true,\n      computesBoundsIncludingLocation: true,\n      isSubGraphExpanded: false,\n      handlesDragDropForMembers: true,\n      resizable: true,\n      resizeObjectName: 'Placeholder'\n    },\n    //Binding the common property\n    ...this.getCommonBindings(), new go.Binding('background', 'isHighlighted', h => h ? 'rgba(255,0,0,0.2)' : 'transparent').ofObject(), this.createPackageGroupShape(), this.createCommentGroupPanels());\n  }\n  /**\n   * Creates and returns a node template for the palette.\n   *\n   * @private\n   * @returns {go.Node} The configured node template.\n   * @memberof DiagramEditorComponent\n   */\n  getComponentNodeTemplate() {\n    return this.$(go.Node, 'Horizontal', this.$(go.TextBlock,\n    // Replace Picture with TextBlock for Font Awesome icons\n    {\n      width: 14,\n      height: 14,\n      font: '13px FontAwesome',\n      textAlign: 'center',\n      verticalAlignment: go.Spot.Center,\n      margin: new go.Margin(0, 5, 0, 0) // Adjust margin as needed\n    }, new go.Binding('text', 'icon') // Bind to the icon property\n    ), this.$(go.TextBlock, {\n      stroke: 'black',\n      font: '10pt sans-serif',\n      editable: true,\n      isMultiline: false,\n      cursor: 'pointer',\n      portId: ''\n    }, new go.Binding('text', 'name')));\n  }\n  /**\n   * Creates and returns a group template for the palette.\n   *\n   * @private\n   * @returns {go.Group} The configured group template.\n   * @memberof DiagramEditorComponent\n   */\n  getComponentGroupTemplate() {\n    return this.$(go.Group, 'Horizontal', {\n      cursor: 'pointer'\n    }, new go.Binding('selectable', 'editable').makeTwoWay(), this.$(go.TextBlock,\n    // Replace Picture with TextBlock for Font Awesome icons\n    {\n      height: 14,\n      width: 14,\n      font: '13px FontAwesome',\n      textAlign: 'center',\n      verticalAlignment: go.Spot.Center,\n      margin: new go.Margin(0, 5, 0, 0) // Adjust margin as needed\n    }, new go.Binding('text', 'icon') // Bind to the icon property\n    ), this.$(go.TextBlock, {\n      name: 'Label',\n      stroke: 'black',\n      font: '10pt sans-serif',\n      alignment: go.Spot.Right,\n      alignmentFocus: go.Spot.Left\n    }, new go.Binding('text', 'name')));\n  }\n  createAssociativeClassGroupTemplate() {\n    return this.$(go.Group, 'Auto', {\n      selectionAdornmentTemplate: this.$(go.Adornment, 'Spot', this.createSelectionBorderPanel(), this.createActionButtonPanel(true, true)),\n      ...this.getClassOrAssociativeProperties()\n    },\n    //Binding the common property\n    ...this.getCommonBindings(), this.createGroupShape({\n      stroke: 'black',\n      strokeWidth: 1.5,\n      strokeDashArray: [4, 8]\n    }, true), this.createGroupPanels(), ...linkPortList.map(linkPort => this.createPort(linkPort.portId, linkPort.alignment, linkPort.isFromLinkable, linkPort.isToLinkable)));\n  }\n  createLinkLabelTemplate() {\n    return this.$(go.Node, {\n      avoidable: true,\n      layerName: 'Foreground',\n      movable: false,\n      deletable: false,\n      fromLinkableSelfNode: false\n    }, new go.Binding('selectable', 'editable').makeTwoWay(), this.$(go.Shape, 'Ellipse', {\n      width: 0.5,\n      height: 0.5,\n      fill: 'rgba(0,0,0,0.01)',\n      stroke: 'rgba(0,0,0,0.05)',\n      portId: '',\n      cursor: 'pointer',\n      fromLinkable: false\n    }, new go.Binding('toLinkable', 'editable').makeTwoWay()));\n  }\n  /**\n   * Retrieves common properties and event handlers shared across all group elements in the diagram.\n   * @private\n   * @return {*}  {go.ObjectData} An object containing properties and event handlers for group elements.\n   * @memberof GojsService\n   */\n  getCommonGroupProperties() {\n    return {\n      mouseDragEnter: () => this.handleMouseDragEnter.bind(this),\n      mouseDragLeave: () => this.handleMouseDragLeave.bind(this),\n      computesBoundsAfterDrag: true,\n      computesBoundsIncludingLocation: true,\n      mouseDrop: this.handleDropCompletion,\n      resizeObjectName: 'Shape',\n      selectionChanged: this.handleSelectionChanged.bind(this)\n    };\n  }\n  /**\n   * Retrieves properties configuration for Class and Associative Class elements in the diagram.\n   * @private\n   * @return {*}  {go.ObjectData} An object containing properties and event handlers for Class elements\n   * @memberof GojsService\n   */\n  getClassOrAssociativeProperties() {\n    return {\n      ...this.getCommonGroupProperties(),\n      mouseEnter: (_e, node) => this.toggleSmallPortsVisibility(node, true),\n      mouseLeave: (_e, node) => this.toggleSmallPortsVisibility(node, false),\n      linkValidation: (fromNode, _fromPort, toNode, _toPort, link) => this.goJsCardinalityService.validateGroupLink(fromNode, toNode, link, _fromPort.portId, this._gojsDiagram)\n    };\n  }\n  /**\n   * Returns an array of common two-way bindings used across different GoJS group templates.\n   * @private\n   * @return {*}  {go.Binding[]} An array of GoJS Binding objects configured for two-way data binding\n   * @memberof GojsService\n   */\n  getCommonBindings() {\n    return [new go.Binding('resizable', 'editable').makeTwoWay(),\n    // new go.Binding('id', 'id').makeTwoWay(),\n    new go.Binding('selectable', 'editable').makeTwoWay(), new go.Binding('handlesDragDropForMembers', 'editable').makeTwoWay(), new go.Binding('position', 'position', go.Point.parse).makeTwoWay(go.Point.stringify)];\n  }\n  /**\n   * Configures the palette with the provided templates.\n   * @private\n   * @param {go.Node} paletteTemplate - The node template for the palette.\n   * @param {go.Group} groupTemplate - The group template for the palette.\n   * @memberof DiagramEditorComponent\n   */\n  configureComponentPalette(paletteTemplate, groupTemplate) {\n    paletteConfigs.forEach(config => {\n      if (!go.Palette.fromDiv(config.name)) {\n        const palette = new go.Palette(config.name);\n        // Configure palette settings\n        palette.allowZoom = false;\n        palette.allowDrop = !this._gojsDiagram.toolManager.textEditingTool.isActive;\n        // Set the node template (left-aligned version)\n        palette.nodeTemplate = paletteTemplate;\n        // Set the group template\n        palette.groupTemplate = groupTemplate;\n        // Configure the palette layout as a grid\n        palette.layout = this.$(go.GridLayout, {\n          wrappingColumn: 2,\n          // spacing: new go.Size(10, 10), // Spacing between items\n          alignment: go.GridAlignment.Position // Align items to the grid positions\n        });\n        // Set the model\n        const model = new go.GraphLinksModel(config.data, []);\n        if (config.links) {\n          model.addLinkDataCollection(config.links);\n        }\n        palette.model = model;\n      }\n    });\n  }\n  static #_ = this.ɵfac = function GojsService_Factory(t) {\n    return new (t || GojsService)(i0.ɵɵinject(i1.AccessService), i0.ɵɵinject(i2.DiagramUtils), i0.ɵɵinject(i3.PropertyService), i0.ɵɵinject(i4.EventListenerService), i0.ɵɵinject(i5.GojsClassService), i0.ɵɵinject(i6.GojsEnumerationService), i0.ɵɵinject(i7.GojsLiteralService), i0.ɵɵinject(i8.GojsFolderService), i0.ɵɵinject(i9.GojsAttributeService), i0.ɵɵinject(i10.GojsCardinalityService), i0.ɵɵinject(i11.GojsCommonService), i0.ɵɵinject(i12.GojsCommentService), i0.ɵɵinject(i13.TreeNodeService), i0.ɵɵinject(i14.SnackBarService), i0.ɵɵinject(i15.AppService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GojsService,\n    factory: GojsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["go", "LinkLabelDraggingTool", "linkPortList", "paletteConfigs", "AttributeType", "GoJsNodeIcon", "GojsNodeCategory", "AccessType", "GojsService", "constructor", "accessService", "diagramUtils", "propertyService", "eventListenerService", "goJsClassService", "goJsEnumService", "goJsLiteralService", "goJsFolderService", "goJsAttributeService", "goJsCardinalityService", "goJsCommonService", "gojsCommentService", "treeNodeService", "snackBarService", "appService", "$", "GraphObject", "make", "_hasEditAccessOnly", "_attributeTypes", "_diagrams", "handleDropCompletion", "event", "objectData", "length", "diagram", "currentTool", "doCancel", "selectedObjects", "selection", "each", "obj", "data", "category", "Association", "supportingLevels", "includes", "handleMemberDrop", "isGroup", "handleTopLevelDrop", "remove", "accessTypeChanges", "subscribe", "access", "Viewer", "gojsDiagramChanges", "_gojsDiagram", "getAttributeTypes", "options", "sort", "a", "b", "name", "localeCompare", "undefined", "sensitivity", "model", "commit", "set", "modelData", "currentProjectDiagramsChanges", "diagrams", "clear", "warn", "allowDrop", "initDiagram", "setGojsDiagram", "isReadOnly", "toolManager", "mouseMoveTools", "insertAt", "firstTool", "elt", "removeAt", "isInitProject", "initializeMainDiagram", "addDiagramEventListeners", "div", "addEventListener", "preventDefault", "onDropNode", "dataTransfer", "getData", "nodeData", "JSON", "parse", "Diagram", "stopPropagation", "clientX", "clientY", "dropPoint", "transformViewToDoc", "Point", "Class", "AssociativeClass", "Enumeration", "checkGroupNodeExist", "key", "id", "handleGroupNodeCreationOrUpdate", "position", "stringify", "Folder", "spacing", "children", "getClassesEnumsFromFolder", "nonExistNode", "for<PERSON>ach", "child", "index", "newPoint", "x", "y", "push", "selectMultipleGroupNodeExist", "initPaletteDiagram", "componentNodeTemplate", "getComponentNodeTemplate", "componentGroupTemplate", "getComponentGroupTemplate", "configureComponentPalette", "setupDiagramTools", "initializeItemTemplate", "configureGroupTemplate", "configureLinkTemplate", "configureGroupTemplateAdornment", "setupDiagramModel", "panningTool", "isEnabled", "Panel", "alignment", "Spot", "TopLeft", "stretch", "<PERSON><PERSON><PERSON>", "Fill", "createNameTextBlock", "createCategoryTextBlock", "createTypeTextBlock", "TextBlock", "isMultiline", "Center", "minSize", "Size", "NaN", "margin", "<PERSON><PERSON>", "overflow", "TextOverflow", "El<PERSON><PERSON>", "wrap", "Wrap", "Fit", "textEdited", "textBlock", "oldText", "newText", "trim", "text", "Binding", "makeTwoWay", "stroke", "Attribute", "Operation", "textEditor", "window", "TextEditorSelectBox", "dataType", "attributeOption", "find", "option", "Undefined", "groupTemplate", "createGroupTemplate", "groupTemplateMap", "add", "Package", "createPackageGroupTemplate", "createEnumerationGroupTemplate", "Comment", "createCommentGroupTemplate", "createAssociativeClassGroupTemplate", "nodeTemplateMap", "LinkLabel", "createLinkLabelTemplate", "Group", "getClassOrAssociativeProperties", "getCommonBindings", "createGroupShape", "createGroupPanels", "map", "linkPort", "createPort", "portId", "isFromLinkable", "isToLinkable", "groupNodeData", "isFromLibrary", "handleClassCreationOrUpdate", "handleEnumCreationOrUpdate", "allowTopLevelDrops", "command<PERSON><PERSON>ler", "addTopLevelParts", "handleCommentDrop", "clearSelection", "textEditingTool", "isActive", "handleAttributeDrop", "handleSelectionDeleting", "addMembers", "EnumerationLiteral", "handleLiteralDrop", "removeDiagramListener", "addDeletingEventListener", "deleteSelection", "addDiagramListener", "updateTargetBindings", "handleMouseDragEnter", "_e", "grp", "_prev", "highlightGroup", "createNewFolder", "projectId", "onCreateNewFolder", "handleMouseDragLeave", "_next", "handleSelectionChanged", "node", "transferDataOnSelection", "show", "tool", "draggingTool", "isHighlighted", "canAddMembers", "draggingParts", "toggleSmallPortsVisibility", "ports", "port", "fill", "linkingTool", "archetypeLinkData", "LinkToLink", "additionalProperties", "isAssociative", "bindingProps", "value", "Brush", "updateRGBAColorWithOpacity", "<PERSON><PERSON><PERSON>", "cursor", "createTablePanel", "defaultRowSeparatorStroke", "defaultRowSeparatorStrokeWidth", "createRowColumnDefinitions", "createTitleTextBlock", "createPropertiesPanel", "createMethodsPanel", "RowColumnDefinition", "row", "minimum", "maximum", "separatorStrokeWidth", "separatorStroke", "columnSpan", "font", "alignmentFocus", "editable", "textAlign", "handleTableTextEdited", "bind", "oldString", "newString", "tableData", "part", "updateComment", "updateTemplateClass", "updateEnumerationFromDiagram", "itemTemplate", "items", "filter", "item", "some", "isOutput", "isInput", "figure", "desiredSize", "fromSpot", "toSpot", "fromLinkable", "toLinkable", "fromLinkableSelfNode", "toLinkableSelfNode", "background", "ungroupable", "mouseDragEnter", "mouseDragLeave", "computesBoundsAfterDrag", "computesBoundsIncludingLocation", "mouseDrop", "handlesDragDropForMembers", "resizable", "resizeObjectName", "createPackageGroupShape", "createPackageGroupPanels", "defaultColor", "strokeWidth", "createPackageGroupHeader", "Placeholder", "padding", "LeftCenter", "createCommentGroupPanels", "maxLines", "Infinity", "Clip", "e", "_obj", "Horizontal", "Left", "defaultFont", "opacity", "handleTextEdited", "_oldString", "first", "description", "horiz", "selectionAdornmentTemplate", "Adornment", "createSelectionBorderPanel", "createActionButtonPanel", "getCommonGroupProperties", "createAttributeButton", "isForLiteral", "createActionButton", "addNodeLiteral", "addNodeAttribute", "iconUnicode", "tooltipText", "clickHandler", "click", "toolTip", "Top", "Bottom", "verticalAlignment", "selectedNode", "idTemplateEnumeration", "onCreateLiteral", "linkTemplate", "createLinkTemplate", "linkTemplateMap", "createLinkToLinkTemplate", "Link", "selectionChanged", "customizeLinkTemplate", "createLinkShape", "createLinkNameShape", "createCardinalityTextBlock", "segmentIndex", "segmentOffset", "maxSize", "deletable", "movable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bindingProp", "routing", "Routing", "AvoidsNodes", "corner", "fromEndSegmentLength", "toEndSegmentLength", "curve", "Curve", "JumpOver", "toShortLength", "segmentFraction", "width", "h", "ofObject", "cardinalityText", "choices", "isClass", "actionPanels", "createMethodButton", "createLinkButton", "initiateLinkDrawing", "actionMove", "geometryString", "button", "<PERSON><PERSON><PERSON>", "specificPort", "findPort", "startObject", "doActivate", "console", "error", "initializeDiagramModelData", "isSubGraphExpanded", "Node", "height", "Right", "avoidable", "layerName", "mouseEnter", "mouseLeave", "linkValidation", "fromNode", "_fromPort", "toNode", "_toPort", "link", "validateGroupLink", "paletteTemplate", "config", "Palette", "fromDiv", "palette", "allowZoom", "nodeTemplate", "layout", "GridLayout", "wrappingColumn", "GridAlignment", "Position", "GraphLinksModel", "links", "addLinkDataCollection", "_", "i0", "ɵɵinject", "i1", "AccessService", "i2", "DiagramUtils", "i3", "PropertyService", "i4", "EventListenerService", "i5", "GojsClassService", "i6", "GojsEnumerationService", "i7", "GojsLiteralService", "i8", "GojsFolderService", "i9", "GojsAttributeService", "i10", "GojsCardinalityService", "i11", "GojsCommonService", "i12", "GojsCommentService", "i13", "TreeNodeService", "i14", "SnackBarService", "i15", "AppService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\gojs\\gojs.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport * as go from 'gojs';\r\nimport { LinkLabelDraggingTool } from 'src/app/extensions/LinkLabelDraggingTool';\r\nimport { linkPortList } from 'src/app/shared/configs/linkPortConfig';\r\nimport { paletteConfigs } from 'src/app/shared/configs/palletteConfigs';\r\nimport { AttributeOption, AttributeType } from 'src/app/shared/model/attribute';\r\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramCommentNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType } from 'src/app/shared/model/project';\r\nimport { TreeNode } from 'src/app/shared/model/treeNode';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../access/access.service';\r\nimport { AppService } from '../app.service';\r\nimport { EventListenerService } from '../diagram/event-listener.service';\r\nimport { PropertyService } from '../property/property.service';\r\nimport { SnackBarService } from '../snackbar/snack-bar.service';\r\nimport { TreeNodeService } from '../treeNode/tree-node.service';\r\nimport { GojsCommentService } from './gojs-comment/gojs-comment.service';\r\nimport { GojsAttributeService } from './gojsAttribute/gojs-attribute.service';\r\nimport { GojsCardinalityService } from './gojsCardinality/gojs-cardinality.service';\r\nimport { GojsClassService } from './gojsClass/gojs-class.service';\r\nimport { GojsCommonService } from './gojsCommon/gojs-common.service';\r\nimport { GojsEnumerationService } from './gojsEnumeration/gojs-enumeration.service';\r\nimport { GojsFolderService } from './gojsFolder/gojs-folder.service';\r\nimport { GojsLiteralService } from './gojsLiteral/gojs-literal.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GojsService {\r\n  private _gojsDiagram!: go.Diagram;\r\n  private $ = go.GraphObject.make;\r\n  private _hasEditAccessOnly: boolean = false;\r\n  private _attributeTypes: AttributeOption[] = [];\r\n  private _diagrams: Diagram[] = [];\r\n  constructor(\r\n    private accessService: AccessService,\r\n    private diagramUtils: DiagramUtils,\r\n    private propertyService: PropertyService,\r\n    private eventListenerService: EventListenerService,\r\n    private goJsClassService: GojsClassService,\r\n    private goJsEnumService: GojsEnumerationService,\r\n    private goJsLiteralService: GojsLiteralService,\r\n    private goJsFolderService: GojsFolderService,\r\n    private goJsAttributeService: GojsAttributeService,\r\n    private goJsCardinalityService: GojsCardinalityService,\r\n    private goJsCommonService: GojsCommonService,\r\n    private gojsCommentService: GojsCommentService,\r\n    private treeNodeService: TreeNodeService,\r\n    private snackBarService: SnackBarService,\r\n    private appService: AppService\r\n  ) {\r\n    this.accessService.accessTypeChanges().subscribe((access) => {\r\n      if (access != AccessType.Viewer) {\r\n        this._hasEditAccessOnly = true;\r\n      } else {\r\n        this._hasEditAccessOnly = false;\r\n      }\r\n    });\r\n    this.goJsCommonService.gojsDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this._gojsDiagram = diagram;\r\n    });\r\n    this.diagramUtils.getAttributeTypes().subscribe((options) => {\r\n      this._attributeTypes = options.sort((a, b) =>\r\n        a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })\r\n      );\r\n      if (this._gojsDiagram && this._gojsDiagram.model) {\r\n        this._gojsDiagram.model.commit((model) => {\r\n          model.set(model.modelData, 'attributeTypes', options);\r\n        }, null);\r\n      }\r\n    });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      this._diagrams = diagrams;\r\n      if (this._gojsDiagram && this._diagrams.length == 0) {\r\n        this._gojsDiagram.clear();\r\n        this.snackBarService.warn('snackBar.diagramDeleteInfo');\r\n      }\r\n      if (this._gojsDiagram) {\r\n        this._gojsDiagram.allowDrop =\r\n          this._hasEditAccessOnly && this._diagrams.length > 0;\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   *Load the go js component  with the selected diagram details and set it to the layout container.\r\n   * @private\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  initDiagram(diagram: go.Diagram): void {\r\n    this.goJsCommonService.setGojsDiagram(diagram);\r\n    this._gojsDiagram.isReadOnly = !this._hasEditAccessOnly;\r\n    const toolManager = this._gojsDiagram.toolManager;\r\n    if (this._hasEditAccessOnly) {\r\n      toolManager.mouseMoveTools.insertAt(0, new LinkLabelDraggingTool());\r\n    } else {\r\n      const firstTool = toolManager.mouseMoveTools.elt(0);\r\n      if (firstTool instanceof LinkLabelDraggingTool) {\r\n        toolManager.mouseMoveTools.removeAt(0);\r\n      }\r\n    }\r\n    if (this.appService.isInitProject()) {\r\n      this.initializeMainDiagram();\r\n      this.eventListenerService.addDiagramEventListeners();\r\n\r\n      diagram.div?.addEventListener('dragover', (event) => {\r\n        event.preventDefault();\r\n      });\r\n      diagram.div?.addEventListener('drop', (event) => {\r\n        this.onDropNode(event);\r\n      });\r\n    }\r\n  }\r\n\r\n  onDropNode(event: DragEvent) {\r\n    event.preventDefault();\r\n    const data = event.dataTransfer?.getData('text/plain');\r\n    if (data) {\r\n      const nodeData = JSON.parse(data);\r\n      if (nodeData.category === GojsNodeCategory.Diagram) {\r\n        event.stopPropagation();\r\n        return;\r\n      }\r\n      // Get the drop position in screen coordinates (clientX, clientY)\r\n      const clientX = event.clientX - 300;\r\n      const clientY = event.clientY - 50;\r\n\r\n      // Convert the screen coordinates to diagram coordinates\r\n      const dropPoint = this._gojsDiagram.transformViewToDoc(\r\n        new go.Point(clientX, clientY)\r\n      );\r\n      if (\r\n        nodeData.category === GojsNodeCategory.Class ||\r\n        nodeData.category === GojsNodeCategory.AssociativeClass ||\r\n        nodeData.category === GojsNodeCategory.Enumeration\r\n      ) {\r\n        if (\r\n          this.goJsCommonService.checkGroupNodeExist(\r\n            this._gojsDiagram,\r\n            nodeData\r\n          )\r\n        )\r\n          return;\r\n\r\n        delete nodeData.data.key;\r\n        delete nodeData.data.id;\r\n        this.handleGroupNodeCreationOrUpdate(\r\n          {\r\n            ...nodeData.data,\r\n            position: go.Point.stringify(dropPoint),\r\n          },\r\n          true\r\n        );\r\n      } else if (nodeData.category === GojsNodeCategory.Folder) {\r\n        const spacing = 170;\r\n        const children =\r\n          this.treeNodeService.getClassesEnumsFromFolder(nodeData);\r\n        const nonExistNode: TreeNode[] = [];\r\n        children.forEach((child, index) => {\r\n          const newPoint = new go.Point(\r\n            dropPoint.x + index * spacing,\r\n            dropPoint.y\r\n          );\r\n          if (\r\n            !this.goJsCommonService.checkGroupNodeExist(\r\n              this._gojsDiagram,\r\n              child\r\n            )\r\n          ) {\r\n            this.handleGroupNodeCreationOrUpdate(\r\n              {\r\n                ...child.data,\r\n                position: go.Point.stringify(newPoint),\r\n              },\r\n              true\r\n            );\r\n          } else {\r\n            nonExistNode.push(child);\r\n          }\r\n        });\r\n        this.goJsCommonService.selectMultipleGroupNodeExist(\r\n          this._gojsDiagram,\r\n          nonExistNode\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  initPaletteDiagram(): void {\r\n    const componentNodeTemplate = this.getComponentNodeTemplate();\r\n    const componentGroupTemplate = this.getComponentGroupTemplate();\r\n    this.configureComponentPalette(\r\n      componentNodeTemplate,\r\n      componentGroupTemplate\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Initializes the main diagram with configuration settings and tools.\r\n   * This method sets up the diagram's properties, tools, templates, and event listeners\r\n   * templates, and event listeners.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  initializeMainDiagram(): void {\r\n    this.setupDiagramTools();\r\n    this.initializeItemTemplate();\r\n    this.configureGroupTemplate();\r\n    this.configureLinkTemplate();\r\n    this.configureGroupTemplateAdornment();\r\n    this.setupDiagramModel();\r\n  }\r\n\r\n  /**\r\n   * Sets up tools and context menus for the diagram.\r\n   * @private\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private setupDiagramTools(): void {\r\n    if (\r\n      this._gojsDiagram &&\r\n      this._gojsDiagram.toolManager &&\r\n      this._gojsDiagram.toolManager.panningTool\r\n    ) {\r\n      this._gojsDiagram.toolManager.panningTool.isEnabled =\r\n        this._hasEditAccessOnly;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initializes and returns the item template for the diagram.\r\n   * This template includes text blocks for name, category, and type with appropriate bindings and styles.\r\n   *\r\n   * @private\r\n   * @returns {go.Panel} The configured item template panel.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private initializeItemTemplate(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Horizontal',\r\n      {\r\n        alignment: go.Spot.TopLeft,\r\n        stretch: go.Stretch.Fill,\r\n      },\r\n      this.createNameTextBlock(),\r\n      this.createCategoryTextBlock(),\r\n      this.createTypeTextBlock()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns a text block for the name with bindings and styles.\r\n   *\r\n   * @private\r\n   * @returns {go.TextBlock} The configured name text block.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private createNameTextBlock(): go.TextBlock {\r\n    return this.$(\r\n      go.TextBlock,\r\n      {\r\n        isMultiline: false,\r\n        alignment: go.Spot.Center,\r\n        minSize: new go.Size(50, NaN),\r\n        margin: new go.Margin(3, 10, 0, 10),\r\n        overflow: go.TextOverflow.Ellipsis,\r\n        wrap: go.Wrap.Fit,\r\n        textEdited: (\r\n          textBlock: go.TextBlock,\r\n          oldText: string,\r\n          newText: string\r\n        ) => {\r\n          if (!newText.trim()) {\r\n            // If the new text is empty or contains only spaces, restore the old value\r\n            textBlock.text = oldText;\r\n          }\r\n        },\r\n      },\r\n      new go.Binding('text', 'name').makeTwoWay(),\r\n      new go.Binding('editable', 'editable').makeTwoWay()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns a text block for the category with bindings and styles.\r\n   * @private\r\n   * @returns {go.TextBlock} The configured category text block.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private createCategoryTextBlock(): go.TextBlock {\r\n    return this.$(\r\n      go.TextBlock,\r\n      ':',\r\n      { stroke: 'black' },\r\n      new go.Binding('visible', 'category', (category) => {\r\n        return (\r\n          category === GojsNodeCategory.Attribute ||\r\n          category === GojsNodeCategory.Operation\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns a text block for the type with bindings, styles, and choices.\r\n   *\r\n   * @private\r\n   * @returns {go.TextBlock} The configured type text block.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private createTypeTextBlock(): go.TextBlock {\r\n    return this.$(\r\n      go.TextBlock,\r\n      {\r\n        isMultiline: false,\r\n        alignment: go.Spot.Center,\r\n        margin: new go.Margin(3, 10, 0, 10),\r\n        textEditor: window.TextEditorSelectBox,\r\n      },\r\n      new go.Binding('choices', '', () => {\r\n        return this._attributeTypes.sort();\r\n      }).makeTwoWay(),\r\n      new go.Binding('text', 'dataType', (dataType) => {\r\n        const attributeOption = this._attributeTypes.find(\r\n          (option: AttributeOption) =>\r\n            option.id == dataType || option.name == dataType\r\n        );\r\n        return attributeOption\r\n          ? attributeOption.name\r\n          : `${AttributeType[AttributeType.Undefined]}`;\r\n      }).makeTwoWay(),\r\n      new go.Binding('name', 'dataType', (dataType) => {\r\n        const attributeOption = this._attributeTypes.find(\r\n          (option: AttributeOption) =>\r\n            option.id == dataType || option.name == dataType\r\n        );\r\n        return attributeOption\r\n          ? attributeOption.id\r\n          : `0_${AttributeType[AttributeType.Undefined]}`;\r\n      }).makeTwoWay(),\r\n      new go.Binding('editable', 'editable').makeTwoWay(),\r\n      new go.Binding('visible', 'category', (category) => {\r\n        return (\r\n          category === GojsNodeCategory.Attribute ||\r\n          category === GojsNodeCategory.Operation\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Configures the group template for the diagram with specific behaviors and styles.\r\n   * This method orchestrates the creation of group templates and applies them to the diagram.\r\n   *\r\n   * @private\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private configureGroupTemplate(): void {\r\n    this._gojsDiagram.groupTemplate = this.createGroupTemplate();\r\n    this._gojsDiagram.groupTemplateMap.add(\r\n      GojsNodeCategory.Package,\r\n      this.createPackageGroupTemplate()\r\n    );\r\n    this._gojsDiagram.groupTemplateMap.add(\r\n      GojsNodeCategory.Enumeration,\r\n      this.createEnumerationGroupTemplate()\r\n    );\r\n    this._gojsDiagram.groupTemplateMap.add(\r\n      GojsNodeCategory.Comment,\r\n      this.createCommentGroupTemplate()\r\n    );\r\n    this._gojsDiagram.groupTemplateMap.add(\r\n      GojsNodeCategory.AssociativeClass,\r\n      this.createAssociativeClassGroupTemplate()\r\n    );\r\n    this._gojsDiagram.nodeTemplateMap.add(\r\n      GojsNodeCategory.LinkLabel,\r\n      this.createLinkLabelTemplate()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the main group template for the diagram.\r\n   *\r\n   * @private\r\n   * @returns {go.Group} The configured group template.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private createGroupTemplate(): go.Group {\r\n    return this.$(\r\n      go.Group,\r\n      'Auto',\r\n      {\r\n        ...this.getClassOrAssociativeProperties(),\r\n      },\r\n      //Binding the common property\r\n      ...this.getCommonBindings(),\r\n      this.createGroupShape(),\r\n      this.createGroupPanels(),\r\n      ...linkPortList.map((linkPort) =>\r\n        this.createPort(\r\n          linkPort.portId,\r\n          linkPort.alignment,\r\n          linkPort.isFromLinkable,\r\n          linkPort.isToLinkable\r\n        )\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Handles the completion of a drop operation in the diagram editor\r\n   * @param {go.InputEvent} event is related to drop orientation\r\n   * @param {*} objectData is dragged object data\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  handleDropCompletion = (event: go.InputEvent, objectData: any | null) => {\r\n    if (!this._hasEditAccessOnly || this._diagrams.length == 0) {\r\n      event.diagram.currentTool.doCancel();\r\n      return;\r\n    }\r\n    const selectedObjects = event.diagram.selection;\r\n    selectedObjects.each((obj) => {\r\n      if (obj.data && obj.data.category === GojsNodeCategory.Association) {\r\n        return;\r\n      }\r\n      if (\r\n        objectData &&\r\n        objectData.data.supportingLevels.includes(obj.data.category)\r\n      ) {\r\n        this.handleMemberDrop(obj, objectData, event);\r\n      } else if (obj.data.isGroup) {\r\n        this.handleTopLevelDrop(obj, event);\r\n      } else {\r\n        event.diagram.remove(obj);\r\n        return;\r\n      }\r\n    });\r\n  };\r\n\r\n  /**\r\n   * Creates or updates a class on the diagram.\r\n   *\r\n   * @param classData - The data of the class to be created or updated.\r\n   * @param event - The GoJS input event.\r\n   */\r\n  private handleGroupNodeCreationOrUpdate(\r\n    groupNodeData: go.ObjectData,\r\n    isFromLibrary: boolean,\r\n    event?: go.InputEvent\r\n  ) {\r\n    if (\r\n      groupNodeData['category'] === GojsNodeCategory.Class ||\r\n      groupNodeData['category'] === GojsNodeCategory.AssociativeClass\r\n    ) {\r\n      this.goJsClassService.handleClassCreationOrUpdate(\r\n        groupNodeData as GojsDiagramClassNode,\r\n        this._gojsDiagram,\r\n        isFromLibrary,\r\n        event\r\n      );\r\n    } else if (groupNodeData['category'] === GojsNodeCategory.Enumeration) {\r\n      this.goJsEnumService.handleEnumCreationOrUpdate(\r\n        groupNodeData as GojsDiagramEnumerationNode,\r\n        this._gojsDiagram,\r\n        isFromLibrary,\r\n        event\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles top-level drops in the diagram.\r\n   *\r\n   * @param obj - The dropped object.\r\n   * @param event - The GoJS input event.\r\n   */\r\n  private handleTopLevelDrop(obj: go.Part, event: go.InputEvent) {\r\n    if (obj.data.allowTopLevelDrops === true) {\r\n      event.diagram.commandHandler.addTopLevelParts(\r\n        event.diagram.selection,\r\n        true\r\n      );\r\n      if (\r\n        obj.data.category === GojsNodeCategory.Class ||\r\n        obj.data.category === GojsNodeCategory.Enumeration ||\r\n        obj.data.category === GojsNodeCategory.AssociativeClass\r\n      ) {\r\n        this.handleGroupNodeCreationOrUpdate(obj.data, false, event);\r\n      } else if (obj.data.category === GojsNodeCategory.Comment) {\r\n        this.gojsCommentService.handleCommentDrop(obj.data, this._gojsDiagram);\r\n      }\r\n      event.diagram.clearSelection();\r\n    } else {\r\n      event.diagram.currentTool.doCancel();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles member element drops in the diagram.\r\n   * @param obj - The dropped object.\r\n   * @param objectData - The target object data.\r\n   * @param event - The GoJS input event.\r\n   */\r\n  private handleMemberDrop(\r\n    obj: go.Part,\r\n    objectData: any,\r\n    event: go.InputEvent\r\n  ) {\r\n    if (\r\n      objectData.data.supportingLevels.includes(obj.category) &&\r\n      !event.diagram.toolManager.textEditingTool.isActive\r\n    ) {\r\n      if (\r\n        obj.data.category === GojsNodeCategory.Attribute ||\r\n        obj.data.category === GojsNodeCategory.Operation\r\n      ) {\r\n        this.goJsAttributeService.handleAttributeDrop(\r\n          obj,\r\n          objectData.data,\r\n          event,\r\n          this._gojsDiagram,\r\n          this._hasEditAccessOnly\r\n        );\r\n        this.handleSelectionDeleting(objectData);\r\n      } else if (obj.data.category === GojsNodeCategory.Class) {\r\n        this.handleGroupNodeCreationOrUpdate(obj.data, false, event);\r\n        objectData.addMembers(objectData.diagram.selection, true);\r\n      } else if (obj.data.category === GojsNodeCategory.EnumerationLiteral) {\r\n        this.goJsLiteralService.handleLiteralDrop(\r\n          obj,\r\n          objectData.data,\r\n          event,\r\n          this._gojsDiagram,\r\n          this._hasEditAccessOnly\r\n        );\r\n        this.handleSelectionDeleting(objectData);\r\n      }\r\n    } else {\r\n      const diagram = event.diagram;\r\n      // Remove the dropped node from the diagram\r\n      if (diagram && obj) {\r\n        diagram.remove(obj);\r\n      }\r\n      // Cancel the operation and clear selection\r\n      event.diagram.currentTool.doCancel();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles the selection deleting event for the diagram.\r\n   *\r\n   * @param objectData - The target object data.\r\n   */\r\n  private handleSelectionDeleting(objectData: any) {\r\n    this._gojsDiagram.removeDiagramListener(\r\n      'SelectionDeleting',\r\n      this.eventListenerService.addDeletingEventListener\r\n    );\r\n    objectData.diagram.commandHandler.deleteSelection();\r\n    this._gojsDiagram.addDiagramListener(\r\n      'SelectionDeleting',\r\n      this.eventListenerService.addDeletingEventListener\r\n    );\r\n    this._gojsDiagram.model.updateTargetBindings(objectData);\r\n  }\r\n\r\n  /**\r\n   * Handles mouse drag enter events for groups.\r\n   *\r\n   * @private\r\n   * @param {go.DiagramEvent} _e - The diagram event.\r\n   * @param {go.GraphObject} grp - The group being entered.\r\n   * @param {go.GraphObject} _prev - The previous object.\r\n   */\r\n  private handleMouseDragEnter(\r\n    _e: go.DiagramEvent,\r\n    grp: go.GraphObject,\r\n    _prev: go.GraphObject\r\n  ): void {\r\n    this.highlightGroup(grp, true);\r\n  }\r\n\r\n  createNewFolder(name: string, projectId: number) {\r\n    this.goJsFolderService.onCreateNewFolder(\r\n      name,\r\n      projectId,\r\n      this._hasEditAccessOnly\r\n    );\r\n  }\r\n  /**\r\n   * Handles mouse drag leave events for groups.\r\n   *\r\n   * @private\r\n   * @param {go.DiagramEvent} _e - The diagram event.\r\n   * @param {go.GraphObject} grp - The group being left.\r\n   * @param {go.GraphObject} _next - The next object.\r\n   */\r\n  private handleMouseDragLeave(\r\n    _e: go.DiagramEvent,\r\n    grp: go.GraphObject,\r\n    _next: go.GraphObject\r\n  ): void {\r\n    this.highlightGroup(grp, false);\r\n  }\r\n\r\n  /**\r\n   * Handles selection changes for groups.\r\n   *\r\n   * @private\r\n   * @param {go.Part} node - The node whose selection changed.\r\n   */\r\n  private handleSelectionChanged(node: go.Part): void {\r\n    this.propertyService.transferDataOnSelection(node);\r\n  }\r\n\r\n  /**\r\n   * Group shape color is highlighted or not\r\n   * @param {*} grp is used  to identify whether the element belongs to a group or\r\n   * @param {boolean} show is used for group visible or not\r\n   * @return {boolean}\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private highlightGroup(grp: any, show: boolean): boolean {\r\n    if (!grp) return false;\r\n    // check that the drop may really happen into the Group\r\n    const tool = grp.diagram.toolManager.draggingTool;\r\n    grp.isHighlighted = show && grp.canAddMembers(tool.draggingParts);\r\n    return grp.isHighlighted;\r\n  }\r\n\r\n  /**\r\n   * Toggles the visibility of small ports on a node in the GoJS diagram.\r\n   *\r\n   * @param event - The input event that triggers the visibility change.\r\n   * @param node - The node whose ports will be shown or hidden.\r\n   * @param show - A boolean indicating whether to show or hide the ports.\r\n   */\r\n  private toggleSmallPortsVisibility(node: go.Node, show: boolean): void {\r\n    node.ports.each((port: go.GraphObject) => {\r\n      if (port.portId !== '') {\r\n        (port as go.Shape).fill = show ? 'rgba(0, 0, 0, 0.3)' : null;\r\n      }\r\n    });\r\n    if (this._gojsDiagram && this._gojsDiagram.toolManager.linkingTool) {\r\n      this._gojsDiagram.toolManager.linkingTool.archetypeLinkData = {\r\n        category:\r\n          node.data.category == GojsNodeCategory.AssociativeClass\r\n            ? GojsNodeCategory.LinkToLink\r\n            : GojsNodeCategory.Association,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates the visual shape for groups.\r\n   * @private\r\n   * @returns {go.Shape} The shape configuration.\r\n   */\r\n  private createGroupShape(\r\n    additionalProperties: go.ObjectData = {},\r\n    isAssociative: boolean = false\r\n  ): go.Shape {\r\n    const bindingProps: go.Binding[] = [\r\n      new go.Binding('visible', 'showTablePanel').makeTwoWay(),\r\n      new go.Binding('fromLinkable', 'editable').makeTwoWay(),\r\n      new go.Binding('toLinkable', 'editable').makeTwoWay(),\r\n      new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(),\r\n      new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(),\r\n      new go.Binding('fill', 'color', (value: string) => {\r\n        return this.$(go.Brush, 'Linear', {\r\n          0.0: this.goJsCommonService.updateRGBAColorWithOpacity(value, 0.4),\r\n          1.0: value,\r\n        });\r\n      }),\r\n    ];\r\n    if (!isAssociative) {\r\n      bindingProps.push(new go.Binding('stroke', 'color').makeTwoWay());\r\n    }\r\n    return this.$(\r\n      go.Shape,\r\n      'RoundedRectangle',\r\n      {\r\n        cursor: 'pointer',\r\n        ...additionalProperties,\r\n      },\r\n      ...bindingProps\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the group panel with the specified properties and bindings.\r\n   * @returns {go.Panel} The group panel.\r\n   */\r\n  private createGroupPanels(): go.Panel {\r\n    return this.$(go.Panel, 'Vertical', this.createTablePanel());\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the table panel containing the components of the group panel.\r\n   * @returns {go.Panel} The table panel.\r\n   */\r\n  private createTablePanel(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Table',\r\n      {\r\n        name: 'Shape',\r\n        defaultRowSeparatorStroke: 'black',\r\n        defaultRowSeparatorStrokeWidth: 1,\r\n        portId: '',\r\n        cursor: 'pointer',\r\n        minSize: new go.Size(150, 100),\r\n      },\r\n      this.createRowColumnDefinitions(),\r\n      this.createTitleTextBlock(),\r\n      this.createPropertiesPanel(),\r\n      this.createMethodsPanel(),\r\n      new go.Binding('visible', 'showTablePanel').makeTwoWay(),\r\n      new go.Binding('desiredSize', 'size').makeTwoWay()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the row and column definitions for the table panel.\r\n   * @returns {go.RowColumnDefinition[]} The row and column definitions.\r\n   */\r\n  private createRowColumnDefinitions(): go.RowColumnDefinition[] {\r\n    return [\r\n      this.$(go.RowColumnDefinition, {\r\n        row: 0,\r\n        minimum: 25,\r\n        maximum: 25,\r\n        stretch: go.Stretch.Fill,\r\n        separatorStrokeWidth: 1,\r\n        separatorStroke: 'black',\r\n      }),\r\n      this.$(go.RowColumnDefinition, {\r\n        row: 1,\r\n        minimum: 60,\r\n        stretch: go.Stretch.Fill,\r\n        position: 20,\r\n      }),\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the title TextBlock for the table panel.\r\n   * @returns {go.TextBlock} The title TextBlock.\r\n   */\r\n  private createTitleTextBlock(): go.TextBlock {\r\n    return this.$(\r\n      go.TextBlock,\r\n      {\r\n        row: 0,\r\n        columnSpan: 2,\r\n        font: 'bold 12pt sans-serif',\r\n        minSize: new go.Size(150, NaN),\r\n        margin: new go.Margin(0, 4, 0, 4),\r\n        isMultiline: false,\r\n        wrap: go.Wrap.Fit,\r\n        alignment: go.Spot.Center, // Centers the text in its container\r\n        alignmentFocus: go.Spot.Center, // Ensures the text itself is focused at the center\r\n        editable: true,\r\n        textAlign: 'center',\r\n        name: 'TEXTBLOCK',\r\n        textEdited: this.handleTableTextEdited.bind(this),\r\n      },\r\n      new go.Binding('text', 'name').makeTwoWay(),\r\n      new go.Binding('editable', 'editable').makeTwoWay()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Handles the text edited event to update the property data.\r\n   * @param {go.TextBlock} textBlock - The TextBlock that was edited.\r\n   * @param {string} oldString - The old text string.\r\n   * @param {string} newString - The new text string.\r\n   */\r\n  private handleTableTextEdited(\r\n    textBlock: go.TextBlock,\r\n    oldString: string,\r\n    newString: string\r\n  ): void {\r\n    if (!newString.trim()) {\r\n      // If the new text is empty or contains only spaces, restore the old value\r\n      textBlock.text = oldString;\r\n    } else {\r\n      const tableData = textBlock.part?.data;\r\n      if (tableData.category === GojsNodeCategory.Comment) {\r\n        this.gojsCommentService.updateComment(\r\n          tableData.name,\r\n          newString,\r\n          tableData,\r\n          this._gojsDiagram\r\n        );\r\n      } else {\r\n        this.propertyService.transferDataOnSelection(textBlock.part!);\r\n      }\r\n      if (\r\n        tableData.category === GojsNodeCategory.Class ||\r\n        tableData.category === GojsNodeCategory.AssociativeClass\r\n      ) {\r\n        this.goJsClassService.updateTemplateClass(\r\n          { ...tableData, name: newString },\r\n          this._gojsDiagram\r\n        );\r\n      }\r\n      if (tableData.category === GojsNodeCategory.Enumeration) {\r\n        this.goJsEnumService.updateEnumerationFromDiagram(\r\n          { ...tableData, name: newString },\r\n          this._gojsDiagram\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the properties panel for the table panel.\r\n   * @returns {go.Panel} The properties panel.\r\n   */\r\n  private createPropertiesPanel(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Vertical',\r\n      {\r\n        name: 'PROPERTIES',\r\n        row: 1,\r\n        stretch: go.Stretch.Fill,\r\n        alignment: go.Spot.Center,\r\n        itemTemplate: this.initializeItemTemplate(),\r\n      },\r\n      new go.Binding('itemArray', 'items', (items) =>\r\n        items.filter(\r\n          (item: GojsDiagramLiteralNode | GojsDiagramAttributeNode) =>\r\n            item.category == GojsNodeCategory.EnumerationLiteral ||\r\n            item.category == GojsNodeCategory.Attribute\r\n        )\r\n      ),\r\n      new go.Binding('visible', 'items', (items) =>\r\n        items.some(\r\n          (item: GojsDiagramLiteralNode | GojsDiagramAttributeNode) =>\r\n            item.category === GojsNodeCategory.Attribute ||\r\n            item.category === GojsNodeCategory.EnumerationLiteral\r\n        )\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the methods panel for the table panel.\r\n   * @returns {go.Panel} The methods panel.\r\n   */\r\n  private createMethodsPanel(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Vertical',\r\n      {\r\n        name: 'Methods',\r\n        row: 2,\r\n        stretch: go.Stretch.Fill,\r\n        alignment: go.Spot.Center,\r\n        itemTemplate: this.initializeItemTemplate(),\r\n      },\r\n      new go.Binding('itemArray', 'items', (items) =>\r\n        items.filter(\r\n          (item: GojsDiagramEnumerationNode | GojsDiagramAttributeNode) =>\r\n            item.category == GojsNodeCategory.Operation\r\n        )\r\n      ),\r\n      new go.Binding('visible', 'items', (items) =>\r\n        items.some(\r\n          (item: GojsDiagramEnumerationNode | GojsDiagramAttributeNode) =>\r\n            item.category === GojsNodeCategory.Operation\r\n        )\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a port shape for a node in the GoJS diagram.\r\n   *\r\n   * @param portId - The unique identifier for the port.\r\n   * @param alignment - The alignment spot for the port on the node.\r\n   * @param isOutput - Specifies if the port is for outgoing links.\r\n   * @param isInput - Specifies if the port is for incoming links.\r\n   * @returns A GoJS Shape configured as a port.\r\n   */\r\n  private createPort(\r\n    portId: string,\r\n    alignment: go.Spot,\r\n    isOutput: boolean,\r\n    isInput: boolean\r\n  ): go.Shape {\r\n    return this.$(go.Shape, {\r\n      figure: 'Circle',\r\n      fill: 'transparent',\r\n      stroke: null,\r\n      desiredSize: new go.Size(9, 9),\r\n      alignment: alignment,\r\n      alignmentFocus: alignment,\r\n      portId: portId,\r\n      fromSpot: alignment,\r\n      toSpot: alignment,\r\n      fromLinkable: isOutput,\r\n      toLinkable: isInput,\r\n      fromLinkableSelfNode: isOutput,\r\n      toLinkableSelfNode: isInput,\r\n      cursor: 'pointer',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Creates a specialized group template for packages.\r\n   *\r\n   * @private\r\n   * @returns {go.Group} The package group template.\r\n   */\r\n  private createPackageGroupTemplate(): go.Group {\r\n    return this.$(\r\n      go.Group,\r\n      'Auto',\r\n      {\r\n        background: 'blue',\r\n        ungroupable: true,\r\n        mouseDragEnter: (_e, grp) => this.highlightGroup(grp, true),\r\n        mouseDragLeave: (_e, grp) => this.highlightGroup(grp, false),\r\n        computesBoundsAfterDrag: true,\r\n        computesBoundsIncludingLocation: true,\r\n        mouseDrop: this.handleDropCompletion,\r\n        handlesDragDropForMembers: true,\r\n        resizable: true,\r\n        resizeObjectName: 'Placeholder',\r\n      },\r\n      this.createPackageGroupShape(),\r\n      this.createPackageGroupPanels()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates the visual shape for package groups.\r\n   *\r\n   * @private\r\n   * @returns {go.Shape} The shape configuration.\r\n   */\r\n  private createPackageGroupShape(): go.Shape {\r\n    return this.$(\r\n      go.Shape,\r\n      'RoundedRectangle',\r\n      {\r\n        stroke: this.defaultColor(true),\r\n        fill: this.defaultColor(true),\r\n        strokeWidth: 2,\r\n      },\r\n      new go.Binding('stroke', 'horiz', this.defaultColor),\r\n      new go.Binding('fill', 'horiz', this.defaultColor)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates panels for package groups.\r\n   *\r\n   * @private\r\n   * @returns {go.Panel} The panel configuration.\r\n   */\r\n  private createPackageGroupPanels(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Vertical',\r\n      { name: 'Placeholder' },\r\n      this.createPackageGroupHeader(),\r\n      this.$(go.Placeholder, {\r\n        padding: 5,\r\n        alignment: go.Spot.LeftCenter,\r\n        minSize: new go.Size(200, 150),\r\n      })\r\n    );\r\n  }\r\n\r\n  private createCommentGroupPanels(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Vertical',\r\n      { name: 'Placeholder' },\r\n      this.createPackageGroupHeader(),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          font: 'bold 10pt Helvetica, Arial, sans-serif',\r\n          margin: new go.Margin(10, 8, 4, 8),\r\n          textAlign: 'left',\r\n          maxLines: Infinity,\r\n          minSize: new go.Size(NaN, 200),\r\n          wrap: go.Wrap.Fit,\r\n          // isMultiline: false,\r\n          alignment: go.Spot.TopLeft, // Keep text aligned to the top-left corner\r\n          overflow: go.TextOverflow.Clip, // Prevent text overflow\r\n          stretch: go.Stretch.Fill,\r\n          textEdited: this.handleTableTextEdited.bind(this),\r\n          mouseDrop: (e: go.InputEvent, _obj: go.GraphObject) =>\r\n            e.diagram.currentTool.doCancel(),\r\n        },\r\n        new go.Binding('text', 'description').makeTwoWay(),\r\n        new go.Binding('editable', 'editable').makeTwoWay(),\r\n        new go.Binding('desiredSize', 'size').makeTwoWay()\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates the header panel for package groups.\r\n   *\r\n   * @private\r\n   * @returns {go.Panel} The header panel configuration.\r\n   */\r\n  private createPackageGroupHeader(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Table',\r\n      {\r\n        stretch: go.Stretch.Horizontal,\r\n        background: this.defaultColor(true),\r\n      },\r\n      new go.Binding('background', 'horiz', this.defaultColor),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          alignment: go.Spot.Left,\r\n          stretch: go.Stretch.Horizontal,\r\n          editable: true,\r\n          isMultiline: false,\r\n          wrap: go.Wrap.Fit,\r\n          margin: 5,\r\n          font: this.defaultFont(false),\r\n          opacity: 0.95,\r\n          stroke: '#404040',\r\n          textEdited: this.handleTextEdited.bind(this),\r\n        },\r\n        new go.Binding('font', 'horiz', this.defaultFont),\r\n        new go.Binding('text', 'name').makeTwoWay()\r\n      )\r\n    );\r\n  }\r\n\r\n  private handleTextEdited(\r\n    textBlock: go.TextBlock,\r\n    _oldString: string,\r\n    newString: string\r\n  ): void {\r\n    const tableData: GojsDiagramCommentNode =\r\n      textBlock.diagram?.selection.first()?.data;\r\n    if (tableData.category === GojsNodeCategory.Comment) {\r\n      this.gojsCommentService.updateComment(\r\n        newString,\r\n        tableData.description,\r\n        tableData,\r\n        this._gojsDiagram\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   *For getting the default color\r\n   * @private\r\n   * @param {boolean} horiz is boolean  value for horizontal or vertical layout\r\n   * @return {*}  {string}\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private defaultColor(horiz: boolean): string {\r\n    // a Binding conversion function\r\n    return horiz ? 'rgba(255, 221, 51, 0.55)' : 'rgba(51,211,229, 0.5)';\r\n  }\r\n\r\n  /**\r\n   *For getting the default font\r\n   * @param {boolean} horiz is boolean  value for horizontal or vertical layout\r\n   * @return {*}  {string}\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private defaultFont(horiz: boolean): string {\r\n    // a Binding conversion function\r\n    return horiz ? 'bold 20px sans-serif' : 'bold 16px sans-serif';\r\n  }\r\n\r\n  private createEnumerationGroupTemplate(): go.Group {\r\n    return this.$(\r\n      go.Group,\r\n      'Auto',\r\n      {\r\n        selectionAdornmentTemplate: this.$(\r\n          go.Adornment,\r\n          'Spot',\r\n          this.createSelectionBorderPanel(),\r\n          this.createActionButtonPanel(false)\r\n        ),\r\n        ...this.getCommonGroupProperties(),\r\n      },\r\n      //Binding the common property\r\n      ...this.getCommonBindings(),\r\n      this.createGroupShape(),\r\n      this.createGroupPanels()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a button for adding attributes to the group node.\r\n   *\r\n   * @returns {go.Panel} The configured attribute button panel.\r\n   */\r\n  private createAttributeButton(isForLiteral: boolean): go.Panel {\r\n    return this.createActionButton(\r\n      !isForLiteral ? GoJsNodeIcon.Attribute : GoJsNodeIcon.EnumerationLiteral,\r\n      'Attribute',\r\n      (_e, obj) =>\r\n        isForLiteral\r\n          ? this.addNodeLiteral(obj)\r\n          : this.goJsAttributeService.addNodeAttribute(\r\n              obj,\r\n              'Attribute',\r\n              GojsNodeCategory.Attribute,\r\n              this._gojsDiagram\r\n            )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a button with an image and a specified click handler.\r\n   * This method helps in reducing redundancy by allowing the creation of multiple buttons with similar structure.\r\n   *\r\n   * @param {string} imagePath - The path to the button image.\r\n   * @param {Function} clickHandler - The function to be executed on button click.\r\n   * @returns {go.Panel} The configured button panel.\r\n   */\r\n  private createActionButton(\r\n    iconUnicode: string, // Accepts Font Awesome Unicode (e.g., '\\uf1fe')\r\n    tooltipText: string, // Tooltip text to display on hover\r\n    clickHandler: (e: go.InputEvent, obj: go.GraphObject) => void\r\n  ): go.Panel {\r\n    return this.$(\r\n      'Button',\r\n      {\r\n        click: clickHandler,\r\n        margin: new go.Margin(0, 10, 0, 0), // Adds margin to the right for spacing\r\n        toolTip: this.$(\r\n          go.Adornment, // Tooltip container\r\n          'Auto', // Auto layout for proper sizing\r\n          {\r\n            alignment: go.Spot.Top, // Align tooltip above the button\r\n            alignmentFocus: go.Spot.Bottom, // Tooltip bottom edge aligns with button's top edge\r\n          },\r\n          this.$(\r\n            go.Shape,\r\n            { fill: 'white', stroke: null } // Tooltip background\r\n          ),\r\n          this.$(\r\n            go.TextBlock,\r\n            {\r\n              font: '10pt sans-serif',\r\n              margin: 5,\r\n              textAlign: 'center',\r\n              wrap: go.Wrap.Fit, // Allow text wrapping if necessary\r\n              alignment: go.Spot.Top, // Align tooltip above the button\r\n              alignmentFocus: go.Spot.Top, // Tooltip bottom edge aligns with button's top edge\r\n            },\r\n            tooltipText // Tooltip text content\r\n          )\r\n        ),\r\n      },\r\n      this.$(\r\n        go.TextBlock, // Use a TextBlock for Font Awesome icons\r\n        {\r\n          font: '14px FontAwesome', // Font for Font Awesome icons\r\n          text: iconUnicode, // Set the icon Unicode\r\n          textAlign: 'center',\r\n          verticalAlignment: go.Spot.Center,\r\n          desiredSize: new go.Size(15, 15), // Adjust size as needed\r\n        }\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Adds a literal node to the currently selected node in the GoJS diagram.\r\n   *\r\n   * This method retrieves the currently selected node in the diagram and uses\r\n   * the `GoJsLiteralService` to create a new literal node associated with the selected node.\r\n   * The `idTemplateEnumeration` is passed to help identify the template enumeration.\r\n   *\r\n   * @param obj - The GoJS `GraphObject` from which the selected node is retrieved.\r\n   */\r\n  private addNodeLiteral(obj: go.GraphObject): void {\r\n    // Get the currently selected node in the diagram\r\n    const selectedNode = obj.diagram!.selection.first()!;\r\n    // Extract the `idTemplateEnumeration` from the selected node's data\r\n    const idTemplateEnumeration = selectedNode.data.idTemplateEnumeration;\r\n    // Use the GoJsLiteralService to create a literal node\r\n    this.goJsLiteralService.onCreateLiteral(\r\n      selectedNode.data,\r\n      'Literal',\r\n      idTemplateEnumeration,\r\n      this._gojsDiagram\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Configures the link template for the diagram with specific behaviors and styles.\r\n   * This method orchestrates the creation of link templates and applies them to the diagram.\r\n   *\r\n   * @private\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private configureLinkTemplate(): void {\r\n    this._gojsDiagram.linkTemplate = this.createLinkTemplate();\r\n    this._gojsDiagram.linkTemplateMap.add(\r\n      GojsNodeCategory.LinkToLink,\r\n      this.createLinkToLinkTemplate()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns the link template for the diagram.\r\n   *\r\n   * @private\r\n   * @returns {go.Link} The configured link template.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private createLinkTemplate(): go.Link {\r\n    return this.$(\r\n      go.Link,\r\n      {\r\n        selectionChanged: this.handleSelectionChanged.bind(this),\r\n      },\r\n      this.customizeLinkTemplate(),\r\n      new go.Binding('selectable', 'editable').makeTwoWay(),\r\n      new go.Binding('reshapable', 'editable').makeTwoWay(),\r\n      new go.Binding('resegmentable', 'editable').makeTwoWay(),\r\n      new go.Binding('relinkableFrom', 'editable').makeTwoWay(),\r\n      new go.Binding('relinkableTo', 'editable').makeTwoWay(),\r\n      new go.Binding('deletable', 'editable').makeTwoWay(),\r\n      new go.Binding('toLinkableDuplicates', 'editable').makeTwoWay(),\r\n      // Instead of 'editable', you might use the following bindings:\r\n      new go.Binding('fromLinkable', 'editable').makeTwoWay(),\r\n      new go.Binding('toLinkable', 'editable').makeTwoWay(),\r\n      new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(),\r\n      new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(),\r\n      this.createLinkShape(),\r\n      this.createLinkNameShape('name'),\r\n      this.createCardinalityTextBlock(\r\n        'Cardinality1',\r\n        0,\r\n        0.5,\r\n        new go.Point(15, -15),\r\n        'cardinalityTo'\r\n      ),\r\n      this.createCardinalityTextBlock(\r\n        'Cardinality2',\r\n        -1,\r\n        0.5,\r\n        new go.Point(-15, -15),\r\n        'cardinalityFrom'\r\n      ),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          // text: 'fromComment',\r\n          segmentIndex: 0,\r\n          segmentOffset: new go.Point(NaN, 15),\r\n          editable: true,\r\n          maxSize: new go.Size(200, NaN),\r\n          overflow: go.TextOverflow.Ellipsis, // Optional: Handle overflow gracefully\r\n          wrap: go.Wrap.Fit,\r\n          isMultiline: false,\r\n        },\r\n        new go.Binding('text', 'fromComment').makeTwoWay()\r\n      ),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          // text: 'toComment',\r\n          segmentIndex: -1,\r\n          segmentOffset: new go.Point(NaN, 15),\r\n          editable: true,\r\n          maxSize: new go.Size(200, NaN),\r\n          overflow: go.TextOverflow.Ellipsis, // Optional: Handle overflow gracefully\r\n          wrap: go.Wrap.Fit,\r\n          isMultiline: false,\r\n        },\r\n        new go.Binding('text', 'toComment').makeTwoWay()\r\n      )\r\n    );\r\n  }\r\n\r\n  private createLinkToLinkTemplate(): go.Link {\r\n    return this.$(\r\n      go.Link,\r\n      {\r\n        deletable: false,\r\n        movable: false,\r\n      },\r\n      new go.Binding('relinkableFrom', 'editable'),\r\n      new go.Binding('relinkableTo', 'editable'),\r\n      new go.Binding('selectable', 'editable'),\r\n      new go.Binding('reshapable', 'editable'),\r\n      this.$(go.Shape, {\r\n        fill: 'black',\r\n        strokeWidth: 2,\r\n        strokeDashArray: [4, 8],\r\n      })\r\n    );\r\n  }\r\n  private createLinkNameShape(bindingProp: string): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Auto',\r\n      this.$(go.Shape, {\r\n        fill: this.$(go.Brush, 'Radial'),\r\n        stroke: 'transparent',\r\n        background: 'white',\r\n      }),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          textAlign: 'center',\r\n          font: '10pt helvetica, arial, sans-serif',\r\n          stroke: 'black',\r\n          margin: 4,\r\n          isMultiline: false,\r\n          editable: true,\r\n        },\r\n        new go.Binding('text', bindingProp).makeTwoWay(),\r\n        new go.Binding(\r\n          'segmentFraction',\r\n          'labelPosition.segmentFraction'\r\n        ).makeTwoWay()\r\n      ),\r\n      new go.Binding(\r\n        'segmentOffset',\r\n        'segmentOffset',\r\n        go.Point.parse\r\n      ).makeTwoWay(go.Point.stringify)\r\n    );\r\n  }\r\n  /**\r\n   * For customize the link shape and style in pallette\r\n   * @private\r\n   * @returns {go.Link}\r\n   * @memberOf DiagramEditorComponent\r\n   */\r\n  private customizeLinkTemplate(): go.Link {\r\n    return {\r\n      routing: go.Routing.AvoidsNodes,\r\n      corner: 5,\r\n      fromEndSegmentLength: 30,\r\n      toEndSegmentLength: 30,\r\n      curve: go.Curve.JumpOver,\r\n      toShortLength: 4,\r\n    } as go.Link;\r\n  }\r\n\r\n  /**\r\n   * Creates a shape for the link.\r\n   * @private\r\n   * @returns {go.Shape} The shape configuration.\r\n   */\r\n  private createLinkShape(): go.Shape {\r\n    return this.$(\r\n      go.Shape,\r\n      {\r\n        segmentFraction: 10,\r\n        width: 100,\r\n        strokeWidth: 1.2, // Add a slightly thicker stroke for better visibility\r\n      },\r\n      // Add color bindings\r\n      new go.Binding('stroke', 'color').makeTwoWay(),\r\n      new go.Binding('strokeWidth', 'isHighlighted', (h) =>\r\n        h ? 2.5 : 1.5\r\n      ).ofObject()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a text block for cardinality with specified bindings, choices, and styles.\r\n   * @private\r\n   * @param {string} name - The name of the text block.\r\n   * @param {number} segmentIndex - The segment index.\r\n   * @param {number} segmentFraction - The segment fraction.\r\n   * @param {go.Point} segmentOffset - The segment offset.\r\n   * @param {string} cardinalityText - The cardinality name\r\n   * @returns {go.TextBlock} The configured cardinality text block.\r\n   */\r\n  private createCardinalityTextBlock(\r\n    name: string,\r\n    segmentIndex: number,\r\n    segmentFraction: number,\r\n    segmentOffset: go.Point,\r\n    cardinalityText: string\r\n  ): go.TextBlock {\r\n    return this.$(\r\n      go.TextBlock,\r\n      {\r\n        name: name,\r\n        segmentIndex: segmentIndex,\r\n        segmentFraction: segmentFraction,\r\n        segmentOffset: segmentOffset,\r\n        textEditor: window.TextEditorSelectBox,\r\n        choices: ['0..1', '1', '*', '1..*'],\r\n      },\r\n      new go.Binding('text', cardinalityText).makeTwoWay(),\r\n      new go.Binding('choices', 'choices').makeTwoWay(),\r\n      new go.Binding('editable', 'editable').makeTwoWay()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Configures the selection adornment template for group nodes in the diagram.\r\n   * The adornment includes a border and buttons for adding attributes, methods, and drawing links.\r\n   *\r\n   * @private\r\n   *\r\n   * @memberOf DiagramEditorComponent\r\n   */\r\n  private configureGroupTemplateAdornment(): void {\r\n    this._gojsDiagram.groupTemplate.selectionAdornmentTemplate = this.$(\r\n      go.Adornment,\r\n      'Spot',\r\n      this.createSelectionBorderPanel(),\r\n      this.createActionButtonPanel(true, false)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates an Auto Panel that serves as the background for the adornment,\r\n   * with a border to highlight the selected group node.\r\n   * @private\r\n   *\r\n   * @returns {go.Panel} The configured Auto Panel.\r\n   * @memberOf DiagramEditorComponent\r\n   */\r\n  private createSelectionBorderPanel(): go.Panel {\r\n    return this.$(\r\n      go.Panel,\r\n      'Auto',\r\n      this.$(go.Shape, { stroke: 'dodgerblue', strokeWidth: 2, fill: null }),\r\n      this.$(go.Placeholder)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a Horizontal Panel that contains buttons for adding attributes, methods, and drawing links.\r\n   * The panel is aligned at the top of the group node and positioned just below it.\r\n   *\r\n   * @returns {go.Panel} The configured Horizontal Panel.\r\n   */\r\n  private createActionButtonPanel(\r\n    isClass: boolean,\r\n    isAssociative: boolean = false\r\n  ): go.Panel {\r\n    const actionPanels: go.Panel[] = [];\r\n    if (isClass) {\r\n      actionPanels.push(this.createAttributeButton(false));\r\n      actionPanels.push(this.createMethodButton());\r\n      if (!isAssociative) actionPanels.push(this.createLinkButton());\r\n    } else {\r\n      actionPanels.push(this.createAttributeButton(true));\r\n    }\r\n    return this.$(\r\n      go.Panel,\r\n      'Horizontal',\r\n      {\r\n        alignment: go.Spot.Top,\r\n        alignmentFocus: go.Spot.Bottom,\r\n      },\r\n      ...actionPanels\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a button for adding methods to the group node.\r\n   * @returns {go.Panel} The configured method button panel.\r\n   */\r\n  private createMethodButton(): go.Panel {\r\n    return this.createActionButton(\r\n      GoJsNodeIcon.Operation,\r\n      'Method',\r\n      (_e, obj) =>\r\n        this.goJsAttributeService.addNodeAttribute(\r\n          obj,\r\n          'Method',\r\n          GojsNodeCategory.Operation,\r\n          this._gojsDiagram\r\n        )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a button for drawing links between nodes.\r\n   * The button supports both click and drag actions to initiate a link drawing operation.\r\n   * @returns {go.Panel} The configured link button panel.\r\n   */\r\n  private createLinkButton(): go.Panel {\r\n    return this.$(\r\n      'Button',\r\n      {\r\n        click: (e, obj) => this.initiateLinkDrawing(e, obj), // Click on button and then click on target node\r\n        actionMove: (e, obj) => this.initiateLinkDrawing(e, obj), // Drag from button to the target node\r\n        toolTip: this.$(\r\n          go.Adornment, // Tooltip container\r\n          'Auto', // Auto layout for proper sizing\r\n          {\r\n            alignment: go.Spot.Top, // Align tooltip above the button\r\n            alignmentFocus: go.Spot.Bottom, // Tooltip bottom edge aligns with button's top edge\r\n          },\r\n          this.$(\r\n            go.Shape,\r\n            { fill: 'white', stroke: null } // Tooltip background\r\n          ),\r\n          this.$(\r\n            go.TextBlock,\r\n            {\r\n              font: '10pt sans-serif',\r\n              margin: 5,\r\n              textAlign: 'center',\r\n              wrap: go.Wrap.Fit, // Allow text wrapping if necessary\r\n              alignment: go.Spot.Top, // Align tooltip above the button\r\n              alignmentFocus: go.Spot.Top, // Tooltip bottom edge aligns with button's top edge\r\n            },\r\n            'Draw Link' // Tooltip text content\r\n          )\r\n        ),\r\n      },\r\n      this.$(go.Shape, {\r\n        geometryString: 'M0 0 L8 0 8 12 14 12 M12 10 L14 12 12 14', // Link shape geometry\r\n        desiredSize: new go.Size(15, 15),\r\n        fill: 'lightyellow',\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Initiates the linking tool to draw a link from the selected node.\r\n   * @param {go.InputEvent} event - The input event triggering the link creation.\r\n   * @param {go.GraphObject} button - The button that was clicked to start drawing the link.\r\n   */\r\n  private initiateLinkDrawing(event: go.InputEvent, button: any): void {\r\n    const selectedNode = button.part.adornedPart;\r\n    const linkingTool = event.diagram.toolManager.linkingTool;\r\n    const specificPort = selectedNode.findPort('R2');\r\n    if (specificPort) {\r\n      linkingTool.startObject = specificPort;\r\n      event.diagram.currentTool = linkingTool;\r\n      linkingTool.doActivate();\r\n    } else {\r\n      console.error('Port not found on the selected node');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets up the model for the diagram\r\n   *\r\n   * @private\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private setupDiagramModel(): void {\r\n    this.diagramUtils.initializeDiagramModelData(this._gojsDiagram);\r\n  }\r\n\r\n  private createCommentGroupTemplate(): go.Group {\r\n    return this.$(\r\n      go.Group,\r\n      'Auto',\r\n      {\r\n        background: 'blue',\r\n        ungroupable: true,\r\n        computesBoundsAfterDrag: true,\r\n        computesBoundsIncludingLocation: true,\r\n        isSubGraphExpanded: false,\r\n        handlesDragDropForMembers: true,\r\n        resizable: true,\r\n        resizeObjectName: 'Placeholder',\r\n      },\r\n      //Binding the common property\r\n      ...this.getCommonBindings(),\r\n      new go.Binding('background', 'isHighlighted', (h) =>\r\n        h ? 'rgba(255,0,0,0.2)' : 'transparent'\r\n      ).ofObject(),\r\n      this.createPackageGroupShape(),\r\n      this.createCommentGroupPanels()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns a node template for the palette.\r\n   *\r\n   * @private\r\n   * @returns {go.Node} The configured node template.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private getComponentNodeTemplate(): go.Node {\r\n    return this.$(\r\n      go.Node,\r\n      'Horizontal',\r\n      this.$(\r\n        go.TextBlock, // Replace Picture with TextBlock for Font Awesome icons\r\n        {\r\n          width: 14,\r\n          height: 14,\r\n          font: '13px FontAwesome', // Use FontAwesome font\r\n          textAlign: 'center',\r\n          verticalAlignment: go.Spot.Center,\r\n          margin: new go.Margin(0, 5, 0, 0), // Adjust margin as needed\r\n        },\r\n        new go.Binding('text', 'icon') // Bind to the icon property\r\n      ),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          stroke: 'black',\r\n          font: '10pt sans-serif',\r\n          editable: true,\r\n          isMultiline: false,\r\n          cursor: 'pointer',\r\n          portId: '',\r\n        },\r\n        new go.Binding('text', 'name')\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates and returns a group template for the palette.\r\n   *\r\n   * @private\r\n   * @returns {go.Group} The configured group template.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private getComponentGroupTemplate(): go.Group {\r\n    return this.$(\r\n      go.Group,\r\n      'Horizontal',\r\n      {\r\n        cursor: 'pointer',\r\n      },\r\n      new go.Binding('selectable', 'editable').makeTwoWay(),\r\n      this.$(\r\n        go.TextBlock, // Replace Picture with TextBlock for Font Awesome icons\r\n        {\r\n          height: 14,\r\n          width: 14,\r\n          font: '13px FontAwesome', // Use FontAwesome font\r\n          textAlign: 'center',\r\n          verticalAlignment: go.Spot.Center,\r\n          margin: new go.Margin(0, 5, 0, 0), // Adjust margin as needed\r\n        },\r\n        new go.Binding('text', 'icon') // Bind to the icon property\r\n      ),\r\n      this.$(\r\n        go.TextBlock,\r\n        {\r\n          name: 'Label',\r\n          stroke: 'black',\r\n          font: '10pt sans-serif',\r\n          alignment: go.Spot.Right,\r\n          alignmentFocus: go.Spot.Left,\r\n        },\r\n        new go.Binding('text', 'name')\r\n      )\r\n    );\r\n  }\r\n\r\n  private createAssociativeClassGroupTemplate(): go.Group {\r\n    return this.$(\r\n      go.Group,\r\n      'Auto',\r\n      {\r\n        selectionAdornmentTemplate: this.$(\r\n          go.Adornment,\r\n          'Spot',\r\n          this.createSelectionBorderPanel(),\r\n          this.createActionButtonPanel(true, true)\r\n        ),\r\n        ...this.getClassOrAssociativeProperties(),\r\n      },\r\n      //Binding the common property\r\n      ...this.getCommonBindings(),\r\n      this.createGroupShape(\r\n        {\r\n          stroke: 'black',\r\n          strokeWidth: 1.5,\r\n          strokeDashArray: [4, 8],\r\n        },\r\n        true\r\n      ),\r\n      this.createGroupPanels(),\r\n      ...linkPortList.map((linkPort) =>\r\n        this.createPort(\r\n          linkPort.portId,\r\n          linkPort.alignment,\r\n          linkPort.isFromLinkable,\r\n          linkPort.isToLinkable\r\n        )\r\n      )\r\n    );\r\n  }\r\n\r\n  private createLinkLabelTemplate(): go.Node {\r\n    return this.$(\r\n      go.Node,\r\n      {\r\n        avoidable: true,\r\n        layerName: 'Foreground',\r\n        movable: false,\r\n        deletable: false,\r\n        fromLinkableSelfNode: false,\r\n      },\r\n      new go.Binding('selectable', 'editable').makeTwoWay(),\r\n      this.$(\r\n        go.Shape,\r\n        'Ellipse',\r\n        {\r\n          width: 0.5,\r\n          height: 0.5,\r\n          fill: 'rgba(0,0,0,0.01)', // Nearly transparent fill\r\n          stroke: 'rgba(0,0,0,0.05)', // Very light stroke\r\n          portId: '',\r\n          cursor: 'pointer',\r\n          fromLinkable: false,\r\n        },\r\n        new go.Binding('toLinkable', 'editable').makeTwoWay()\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Retrieves common properties and event handlers shared across all group elements in the diagram.\r\n   * @private\r\n   * @return {*}  {go.ObjectData} An object containing properties and event handlers for group elements.\r\n   * @memberof GojsService\r\n   */\r\n  private getCommonGroupProperties(): go.ObjectData {\r\n    return {\r\n      mouseDragEnter: () => this.handleMouseDragEnter.bind(this),\r\n      mouseDragLeave: () => this.handleMouseDragLeave.bind(this),\r\n      computesBoundsAfterDrag: true,\r\n      computesBoundsIncludingLocation: true,\r\n      mouseDrop: this.handleDropCompletion,\r\n      resizeObjectName: 'Shape',\r\n      selectionChanged: this.handleSelectionChanged.bind(this),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Retrieves properties configuration for Class and Associative Class elements in the diagram.\r\n   * @private\r\n   * @return {*}  {go.ObjectData} An object containing properties and event handlers for Class elements\r\n   * @memberof GojsService\r\n   */\r\n  private getClassOrAssociativeProperties(): go.ObjectData {\r\n    return {\r\n      ...this.getCommonGroupProperties(),\r\n      mouseEnter: (_e: MouseEvent, node: go.Node) =>\r\n        this.toggleSmallPortsVisibility(node, true),\r\n      mouseLeave: (_e: MouseEvent, node: go.Node) =>\r\n        this.toggleSmallPortsVisibility(node, false),\r\n      linkValidation: (\r\n        fromNode: go.Node,\r\n        _fromPort: go.GraphObject,\r\n        toNode: go.Node,\r\n        _toPort: go.GraphObject,\r\n        link: go.Link\r\n      ) =>\r\n        this.goJsCardinalityService.validateGroupLink(\r\n          fromNode,\r\n          toNode,\r\n          link,\r\n          _fromPort.portId,\r\n          this._gojsDiagram\r\n        ),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Returns an array of common two-way bindings used across different GoJS group templates.\r\n   * @private\r\n   * @return {*}  {go.Binding[]} An array of GoJS Binding objects configured for two-way data binding\r\n   * @memberof GojsService\r\n   */\r\n  private getCommonBindings(): go.Binding[] {\r\n    return [\r\n      new go.Binding('resizable', 'editable').makeTwoWay(),\r\n      // new go.Binding('id', 'id').makeTwoWay(),\r\n      new go.Binding('selectable', 'editable').makeTwoWay(),\r\n      new go.Binding('handlesDragDropForMembers', 'editable').makeTwoWay(),\r\n      new go.Binding('position', 'position', go.Point.parse).makeTwoWay(\r\n        go.Point.stringify\r\n      ),\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configures the palette with the provided templates.\r\n   * @private\r\n   * @param {go.Node} paletteTemplate - The node template for the palette.\r\n   * @param {go.Group} groupTemplate - The group template for the palette.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private configureComponentPalette(\r\n    paletteTemplate: go.Node,\r\n    groupTemplate: go.Group\r\n  ): void {\r\n    paletteConfigs.forEach((config) => {\r\n      if (!go.Palette.fromDiv(config.name)) {\r\n        const palette = new go.Palette(config.name);\r\n\r\n        // Configure palette settings\r\n        palette.allowZoom = false;\r\n        palette.allowDrop =\r\n          !this._gojsDiagram.toolManager.textEditingTool.isActive;\r\n\r\n        // Set the node template (left-aligned version)\r\n        palette.nodeTemplate = paletteTemplate;\r\n\r\n        // Set the group template\r\n        palette.groupTemplate = groupTemplate;\r\n\r\n        // Configure the palette layout as a grid\r\n        palette.layout = this.$(go.GridLayout, {\r\n          wrappingColumn: 2, // Number of columns in the grid\r\n          // spacing: new go.Size(10, 10), // Spacing between items\r\n          alignment: go.GridAlignment.Position, // Align items to the grid positions\r\n        });\r\n\r\n        // Set the model\r\n        const model = new go.GraphLinksModel(config.data, []);\r\n        if (config.links) {\r\n          model.addLinkDataCollection(config.links);\r\n        }\r\n        palette.model = model;\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAO,KAAKA,EAAE,MAAM,MAAM;AAC1B,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,cAAc,QAAQ,wCAAwC;AACvE,SAA0BC,aAAa,QAAQ,gCAAgC;AAC/E,SAASC,YAAY,QAAQ,6BAA6B;AAE1D,SAMEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;AAoBzD,OAAM,MAAOC,WAAW;EAMtBC,YACUC,aAA4B,EAC5BC,YAA0B,EAC1BC,eAAgC,EAChCC,oBAA0C,EAC1CC,gBAAkC,EAClCC,eAAuC,EACvCC,kBAAsC,EACtCC,iBAAoC,EACpCC,oBAA0C,EAC1CC,sBAA8C,EAC9CC,iBAAoC,EACpCC,kBAAsC,EACtCC,eAAgC,EAChCC,eAAgC,EAChCC,UAAsB;IAdtB,KAAAd,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,UAAU,GAAVA,UAAU;IAnBZ,KAAAC,CAAC,GAAGzB,EAAE,CAAC0B,WAAW,CAACC,IAAI;IACvB,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAC,SAAS,GAAc,EAAE;IAyXjC;;;;;;IAMA,KAAAC,oBAAoB,GAAG,CAACC,KAAoB,EAAEC,UAAsB,KAAI;MACtE,IAAI,CAAC,IAAI,CAACL,kBAAkB,IAAI,IAAI,CAACE,SAAS,CAACI,MAAM,IAAI,CAAC,EAAE;QAC1DF,KAAK,CAACG,OAAO,CAACC,WAAW,CAACC,QAAQ,EAAE;QACpC;;MAEF,MAAMC,eAAe,GAAGN,KAAK,CAACG,OAAO,CAACI,SAAS;MAC/CD,eAAe,CAACE,IAAI,CAAEC,GAAG,IAAI;QAC3B,IAAIA,GAAG,CAACC,IAAI,IAAID,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACsC,WAAW,EAAE;UAClE;;QAEF,IACEX,UAAU,IACVA,UAAU,CAACS,IAAI,CAACG,gBAAgB,CAACC,QAAQ,CAACL,GAAG,CAACC,IAAI,CAACC,QAAQ,CAAC,EAC5D;UACA,IAAI,CAACI,gBAAgB,CAACN,GAAG,EAAER,UAAU,EAAED,KAAK,CAAC;SAC9C,MAAM,IAAIS,GAAG,CAACC,IAAI,CAACM,OAAO,EAAE;UAC3B,IAAI,CAACC,kBAAkB,CAACR,GAAG,EAAET,KAAK,CAAC;SACpC,MAAM;UACLA,KAAK,CAACG,OAAO,CAACe,MAAM,CAACT,GAAG,CAAC;UACzB;;MAEJ,CAAC,CAAC;IACJ,CAAC;IAnYC,IAAI,CAAC/B,aAAa,CAACyC,iBAAiB,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC1D,IAAIA,MAAM,IAAI9C,UAAU,CAAC+C,MAAM,EAAE;QAC/B,IAAI,CAAC1B,kBAAkB,GAAG,IAAI;OAC/B,MAAM;QACL,IAAI,CAACA,kBAAkB,GAAG,KAAK;;IAEnC,CAAC,CAAC;IACF,IAAI,CAACR,iBAAiB,CAACmC,kBAAkB,EAAE,CAACH,SAAS,CAAEjB,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACqB,YAAY,GAAGrB,OAAO;IAC1C,CAAC,CAAC;IACF,IAAI,CAACxB,YAAY,CAAC8C,iBAAiB,EAAE,CAACL,SAAS,CAAEM,OAAO,IAAI;MAC1D,IAAI,CAAC7B,eAAe,GAAG6B,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACvCD,CAAC,CAACE,IAAI,CAACC,aAAa,CAACF,CAAC,CAACC,IAAI,EAAEE,SAAS,EAAE;QAAEC,WAAW,EAAE;MAAM,CAAE,CAAC,CACjE;MACD,IAAI,IAAI,CAACT,YAAY,IAAI,IAAI,CAACA,YAAY,CAACU,KAAK,EAAE;QAChD,IAAI,CAACV,YAAY,CAACU,KAAK,CAACC,MAAM,CAAED,KAAK,IAAI;UACvCA,KAAK,CAACE,GAAG,CAACF,KAAK,CAACG,SAAS,EAAE,gBAAgB,EAAEX,OAAO,CAAC;QACvD,CAAC,EAAE,IAAI,CAAC;;IAEZ,CAAC,CAAC;IACF,IAAI,CAAC/C,YAAY,CAAC2D,6BAA6B,EAAE,CAAClB,SAAS,CAAEmB,QAAQ,IAAI;MACvE,IAAI,CAACzC,SAAS,GAAGyC,QAAQ;MACzB,IAAI,IAAI,CAACf,YAAY,IAAI,IAAI,CAAC1B,SAAS,CAACI,MAAM,IAAI,CAAC,EAAE;QACnD,IAAI,CAACsB,YAAY,CAACgB,KAAK,EAAE;QACzB,IAAI,CAACjD,eAAe,CAACkD,IAAI,CAAC,4BAA4B,CAAC;;MAEzD,IAAI,IAAI,CAACjB,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,CAACkB,SAAS,GACzB,IAAI,CAAC9C,kBAAkB,IAAI,IAAI,CAACE,SAAS,CAACI,MAAM,GAAG,CAAC;;IAE1D,CAAC,CAAC;EACJ;EAEA;;;;;EAKAyC,WAAWA,CAACxC,OAAmB;IAC7B,IAAI,CAACf,iBAAiB,CAACwD,cAAc,CAACzC,OAAO,CAAC;IAC9C,IAAI,CAACqB,YAAY,CAACqB,UAAU,GAAG,CAAC,IAAI,CAACjD,kBAAkB;IACvD,MAAMkD,WAAW,GAAG,IAAI,CAACtB,YAAY,CAACsB,WAAW;IACjD,IAAI,IAAI,CAAClD,kBAAkB,EAAE;MAC3BkD,WAAW,CAACC,cAAc,CAACC,QAAQ,CAAC,CAAC,EAAE,IAAI/E,qBAAqB,EAAE,CAAC;KACpE,MAAM;MACL,MAAMgF,SAAS,GAAGH,WAAW,CAACC,cAAc,CAACG,GAAG,CAAC,CAAC,CAAC;MACnD,IAAID,SAAS,YAAYhF,qBAAqB,EAAE;QAC9C6E,WAAW,CAACC,cAAc,CAACI,QAAQ,CAAC,CAAC,CAAC;;;IAG1C,IAAI,IAAI,CAAC3D,UAAU,CAAC4D,aAAa,EAAE,EAAE;MACnC,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACxE,oBAAoB,CAACyE,wBAAwB,EAAE;MAEpDnD,OAAO,CAACoD,GAAG,EAAEC,gBAAgB,CAAC,UAAU,EAAGxD,KAAK,IAAI;QAClDA,KAAK,CAACyD,cAAc,EAAE;MACxB,CAAC,CAAC;MACFtD,OAAO,CAACoD,GAAG,EAAEC,gBAAgB,CAAC,MAAM,EAAGxD,KAAK,IAAI;QAC9C,IAAI,CAAC0D,UAAU,CAAC1D,KAAK,CAAC;MACxB,CAAC,CAAC;;EAEN;EAEA0D,UAAUA,CAAC1D,KAAgB;IACzBA,KAAK,CAACyD,cAAc,EAAE;IACtB,MAAM/C,IAAI,GAAGV,KAAK,CAAC2D,YAAY,EAAEC,OAAO,CAAC,YAAY,CAAC;IACtD,IAAIlD,IAAI,EAAE;MACR,MAAMmD,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACrD,IAAI,CAAC;MACjC,IAAImD,QAAQ,CAAClD,QAAQ,KAAKrC,gBAAgB,CAAC0F,OAAO,EAAE;QAClDhE,KAAK,CAACiE,eAAe,EAAE;QACvB;;MAEF;MACA,MAAMC,OAAO,GAAGlE,KAAK,CAACkE,OAAO,GAAG,GAAG;MACnC,MAAMC,OAAO,GAAGnE,KAAK,CAACmE,OAAO,GAAG,EAAE;MAElC;MACA,MAAMC,SAAS,GAAG,IAAI,CAAC5C,YAAY,CAAC6C,kBAAkB,CACpD,IAAIrG,EAAE,CAACsG,KAAK,CAACJ,OAAO,EAAEC,OAAO,CAAC,CAC/B;MACD,IACEN,QAAQ,CAAClD,QAAQ,KAAKrC,gBAAgB,CAACiG,KAAK,IAC5CV,QAAQ,CAAClD,QAAQ,KAAKrC,gBAAgB,CAACkG,gBAAgB,IACvDX,QAAQ,CAAClD,QAAQ,KAAKrC,gBAAgB,CAACmG,WAAW,EAClD;QACA,IACE,IAAI,CAACrF,iBAAiB,CAACsF,mBAAmB,CACxC,IAAI,CAAClD,YAAY,EACjBqC,QAAQ,CACT,EAED;QAEF,OAAOA,QAAQ,CAACnD,IAAI,CAACiE,GAAG;QACxB,OAAOd,QAAQ,CAACnD,IAAI,CAACkE,EAAE;QACvB,IAAI,CAACC,+BAA+B,CAClC;UACE,GAAGhB,QAAQ,CAACnD,IAAI;UAChBoE,QAAQ,EAAE9G,EAAE,CAACsG,KAAK,CAACS,SAAS,CAACX,SAAS;SACvC,EACD,IAAI,CACL;OACF,MAAM,IAAIP,QAAQ,CAAClD,QAAQ,KAAKrC,gBAAgB,CAAC0G,MAAM,EAAE;QACxD,MAAMC,OAAO,GAAG,GAAG;QACnB,MAAMC,QAAQ,GACZ,IAAI,CAAC5F,eAAe,CAAC6F,yBAAyB,CAACtB,QAAQ,CAAC;QAC1D,MAAMuB,YAAY,GAAe,EAAE;QACnCF,QAAQ,CAACG,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;UAChC,MAAMC,QAAQ,GAAG,IAAIxH,EAAE,CAACsG,KAAK,CAC3BF,SAAS,CAACqB,CAAC,GAAGF,KAAK,GAAGN,OAAO,EAC7Bb,SAAS,CAACsB,CAAC,CACZ;UACD,IACE,CAAC,IAAI,CAACtG,iBAAiB,CAACsF,mBAAmB,CACzC,IAAI,CAAClD,YAAY,EACjB8D,KAAK,CACN,EACD;YACA,IAAI,CAACT,+BAA+B,CAClC;cACE,GAAGS,KAAK,CAAC5E,IAAI;cACboE,QAAQ,EAAE9G,EAAE,CAACsG,KAAK,CAACS,SAAS,CAACS,QAAQ;aACtC,EACD,IAAI,CACL;WACF,MAAM;YACLJ,YAAY,CAACO,IAAI,CAACL,KAAK,CAAC;;QAE5B,CAAC,CAAC;QACF,IAAI,CAAClG,iBAAiB,CAACwG,4BAA4B,CACjD,IAAI,CAACpE,YAAY,EACjB4D,YAAY,CACb;;;EAGP;EAEAS,kBAAkBA,CAAA;IAChB,MAAMC,qBAAqB,GAAG,IAAI,CAACC,wBAAwB,EAAE;IAC7D,MAAMC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAACC,yBAAyB,CAC5BJ,qBAAqB,EACrBE,sBAAsB,CACvB;EACH;EAEA;;;;;;EAMA3C,qBAAqBA,CAAA;IACnB,IAAI,CAAC8C,iBAAiB,EAAE;IACxB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,+BAA+B,EAAE;IACtC,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEA;;;;;EAKQL,iBAAiBA,CAAA;IACvB,IACE,IAAI,CAAC3E,YAAY,IACjB,IAAI,CAACA,YAAY,CAACsB,WAAW,IAC7B,IAAI,CAACtB,YAAY,CAACsB,WAAW,CAAC2D,WAAW,EACzC;MACA,IAAI,CAACjF,YAAY,CAACsB,WAAW,CAAC2D,WAAW,CAACC,SAAS,GACjD,IAAI,CAAC9G,kBAAkB;;EAE7B;EAEA;;;;;;;;EAQQwG,sBAAsBA,CAAA;IAC5B,OAAO,IAAI,CAAC3G,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,YAAY,EACZ;MACEC,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACC,OAAO;MAC1BC,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACC;KACrB,EACD,IAAI,CAACC,mBAAmB,EAAE,EAC1B,IAAI,CAACC,uBAAuB,EAAE,EAC9B,IAAI,CAACC,mBAAmB,EAAE,CAC3B;EACH;EAEA;;;;;;;EAOQF,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAACzH,CAAC,CACXzB,EAAE,CAACqJ,SAAS,EACZ;MACEC,WAAW,EAAE,KAAK;MAClBV,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACzBC,OAAO,EAAE,IAAIxJ,EAAE,CAACyJ,IAAI,CAAC,EAAE,EAAEC,GAAG,CAAC;MAC7BC,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MACnCC,QAAQ,EAAE7J,EAAE,CAAC8J,YAAY,CAACC,QAAQ;MAClCC,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;MACjBC,UAAU,EAAEA,CACVC,SAAuB,EACvBC,OAAe,EACfC,OAAe,KACb;QACF,IAAI,CAACA,OAAO,CAACC,IAAI,EAAE,EAAE;UACnB;UACAH,SAAS,CAACI,IAAI,GAAGH,OAAO;;MAE5B;KACD,EACD,IAAIrK,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAACC,UAAU,EAAE,EAC3C,IAAI1K,EAAE,CAACyK,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,CACpD;EACH;EAEA;;;;;;EAMQvB,uBAAuBA,CAAA;IAC7B,OAAO,IAAI,CAAC1H,CAAC,CACXzB,EAAE,CAACqJ,SAAS,EACZ,GAAG,EACH;MAAEsB,MAAM,EAAE;IAAO,CAAE,EACnB,IAAI3K,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,UAAU,EAAG9H,QAAQ,IAAI;MACjD,OACEA,QAAQ,KAAKrC,gBAAgB,CAACsK,SAAS,IACvCjI,QAAQ,KAAKrC,gBAAgB,CAACuK,SAAS;IAE3C,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOQzB,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAAC3H,CAAC,CACXzB,EAAE,CAACqJ,SAAS,EACZ;MACEC,WAAW,EAAE,KAAK;MAClBV,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACzBI,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MACnCkB,UAAU,EAAEC,MAAM,CAACC;KACpB,EACD,IAAIhL,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,EAAE,EAAE,MAAK;MACjC,OAAO,IAAI,CAAC5I,eAAe,CAAC8B,IAAI,EAAE;IACpC,CAAC,CAAC,CAAC+G,UAAU,EAAE,EACf,IAAI1K,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,UAAU,EAAGQ,QAAQ,IAAI;MAC9C,MAAMC,eAAe,GAAG,IAAI,CAACrJ,eAAe,CAACsJ,IAAI,CAC9CC,MAAuB,IACtBA,MAAM,CAACxE,EAAE,IAAIqE,QAAQ,IAAIG,MAAM,CAACtH,IAAI,IAAImH,QAAQ,CACnD;MACD,OAAOC,eAAe,GAClBA,eAAe,CAACpH,IAAI,GACpB,GAAG1D,aAAa,CAACA,aAAa,CAACiL,SAAS,CAAC,EAAE;IACjD,CAAC,CAAC,CAACX,UAAU,EAAE,EACf,IAAI1K,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,UAAU,EAAGQ,QAAQ,IAAI;MAC9C,MAAMC,eAAe,GAAG,IAAI,CAACrJ,eAAe,CAACsJ,IAAI,CAC9CC,MAAuB,IACtBA,MAAM,CAACxE,EAAE,IAAIqE,QAAQ,IAAIG,MAAM,CAACtH,IAAI,IAAImH,QAAQ,CACnD;MACD,OAAOC,eAAe,GAClBA,eAAe,CAACtE,EAAE,GAClB,KAAKxG,aAAa,CAACA,aAAa,CAACiL,SAAS,CAAC,EAAE;IACnD,CAAC,CAAC,CAACX,UAAU,EAAE,EACf,IAAI1K,EAAE,CAACyK,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACnD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,UAAU,EAAG9H,QAAQ,IAAI;MACjD,OACEA,QAAQ,KAAKrC,gBAAgB,CAACsK,SAAS,IACvCjI,QAAQ,KAAKrC,gBAAgB,CAACuK,SAAS;IAE3C,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOQxC,sBAAsBA,CAAA;IAC5B,IAAI,CAAC7E,YAAY,CAAC8H,aAAa,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAC5D,IAAI,CAAC/H,YAAY,CAACgI,gBAAgB,CAACC,GAAG,CACpCnL,gBAAgB,CAACoL,OAAO,EACxB,IAAI,CAACC,0BAA0B,EAAE,CAClC;IACD,IAAI,CAACnI,YAAY,CAACgI,gBAAgB,CAACC,GAAG,CACpCnL,gBAAgB,CAACmG,WAAW,EAC5B,IAAI,CAACmF,8BAA8B,EAAE,CACtC;IACD,IAAI,CAACpI,YAAY,CAACgI,gBAAgB,CAACC,GAAG,CACpCnL,gBAAgB,CAACuL,OAAO,EACxB,IAAI,CAACC,0BAA0B,EAAE,CAClC;IACD,IAAI,CAACtI,YAAY,CAACgI,gBAAgB,CAACC,GAAG,CACpCnL,gBAAgB,CAACkG,gBAAgB,EACjC,IAAI,CAACuF,mCAAmC,EAAE,CAC3C;IACD,IAAI,CAACvI,YAAY,CAACwI,eAAe,CAACP,GAAG,CACnCnL,gBAAgB,CAAC2L,SAAS,EAC1B,IAAI,CAACC,uBAAuB,EAAE,CAC/B;EACH;EAEA;;;;;;;EAOQX,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAAC9J,CAAC,CACXzB,EAAE,CAACmM,KAAK,EACR,MAAM,EACN;MACE,GAAG,IAAI,CAACC,+BAA+B;KACxC;IACD;IACA,GAAG,IAAI,CAACC,iBAAiB,EAAE,EAC3B,IAAI,CAACC,gBAAgB,EAAE,EACvB,IAAI,CAACC,iBAAiB,EAAE,EACxB,GAAGrM,YAAY,CAACsM,GAAG,CAAEC,QAAQ,IAC3B,IAAI,CAACC,UAAU,CACbD,QAAQ,CAACE,MAAM,EACfF,QAAQ,CAAC7D,SAAS,EAClB6D,QAAQ,CAACG,cAAc,EACvBH,QAAQ,CAACI,YAAY,CACtB,CACF,CACF;EACH;EAgCA;;;;;;EAMQhG,+BAA+BA,CACrCiG,aAA4B,EAC5BC,aAAsB,EACtB/K,KAAqB;IAErB,IACE8K,aAAa,CAAC,UAAU,CAAC,KAAKxM,gBAAgB,CAACiG,KAAK,IACpDuG,aAAa,CAAC,UAAU,CAAC,KAAKxM,gBAAgB,CAACkG,gBAAgB,EAC/D;MACA,IAAI,CAAC1F,gBAAgB,CAACkM,2BAA2B,CAC/CF,aAAqC,EACrC,IAAI,CAACtJ,YAAY,EACjBuJ,aAAa,EACb/K,KAAK,CACN;KACF,MAAM,IAAI8K,aAAa,CAAC,UAAU,CAAC,KAAKxM,gBAAgB,CAACmG,WAAW,EAAE;MACrE,IAAI,CAAC1F,eAAe,CAACkM,0BAA0B,CAC7CH,aAA2C,EAC3C,IAAI,CAACtJ,YAAY,EACjBuJ,aAAa,EACb/K,KAAK,CACN;;EAEL;EAEA;;;;;;EAMQiB,kBAAkBA,CAACR,GAAY,EAAET,KAAoB;IAC3D,IAAIS,GAAG,CAACC,IAAI,CAACwK,kBAAkB,KAAK,IAAI,EAAE;MACxClL,KAAK,CAACG,OAAO,CAACgL,cAAc,CAACC,gBAAgB,CAC3CpL,KAAK,CAACG,OAAO,CAACI,SAAS,EACvB,IAAI,CACL;MACD,IACEE,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACiG,KAAK,IAC5C9D,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACmG,WAAW,IAClDhE,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACkG,gBAAgB,EACvD;QACA,IAAI,CAACK,+BAA+B,CAACpE,GAAG,CAACC,IAAI,EAAE,KAAK,EAAEV,KAAK,CAAC;OAC7D,MAAM,IAAIS,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACuL,OAAO,EAAE;QACzD,IAAI,CAACxK,kBAAkB,CAACgM,iBAAiB,CAAC5K,GAAG,CAACC,IAAI,EAAE,IAAI,CAACc,YAAY,CAAC;;MAExExB,KAAK,CAACG,OAAO,CAACmL,cAAc,EAAE;KAC/B,MAAM;MACLtL,KAAK,CAACG,OAAO,CAACC,WAAW,CAACC,QAAQ,EAAE;;EAExC;EAEA;;;;;;EAMQU,gBAAgBA,CACtBN,GAAY,EACZR,UAAe,EACfD,KAAoB;IAEpB,IACEC,UAAU,CAACS,IAAI,CAACG,gBAAgB,CAACC,QAAQ,CAACL,GAAG,CAACE,QAAQ,CAAC,IACvD,CAACX,KAAK,CAACG,OAAO,CAAC2C,WAAW,CAACyI,eAAe,CAACC,QAAQ,EACnD;MACA,IACE/K,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACsK,SAAS,IAChDnI,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACuK,SAAS,EAChD;QACA,IAAI,CAAC3J,oBAAoB,CAACuM,mBAAmB,CAC3ChL,GAAG,EACHR,UAAU,CAACS,IAAI,EACfV,KAAK,EACL,IAAI,CAACwB,YAAY,EACjB,IAAI,CAAC5B,kBAAkB,CACxB;QACD,IAAI,CAAC8L,uBAAuB,CAACzL,UAAU,CAAC;OACzC,MAAM,IAAIQ,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACiG,KAAK,EAAE;QACvD,IAAI,CAACM,+BAA+B,CAACpE,GAAG,CAACC,IAAI,EAAE,KAAK,EAAEV,KAAK,CAAC;QAC5DC,UAAU,CAAC0L,UAAU,CAAC1L,UAAU,CAACE,OAAO,CAACI,SAAS,EAAE,IAAI,CAAC;OAC1D,MAAM,IAAIE,GAAG,CAACC,IAAI,CAACC,QAAQ,KAAKrC,gBAAgB,CAACsN,kBAAkB,EAAE;QACpE,IAAI,CAAC5M,kBAAkB,CAAC6M,iBAAiB,CACvCpL,GAAG,EACHR,UAAU,CAACS,IAAI,EACfV,KAAK,EACL,IAAI,CAACwB,YAAY,EACjB,IAAI,CAAC5B,kBAAkB,CACxB;QACD,IAAI,CAAC8L,uBAAuB,CAACzL,UAAU,CAAC;;KAE3C,MAAM;MACL,MAAME,OAAO,GAAGH,KAAK,CAACG,OAAO;MAC7B;MACA,IAAIA,OAAO,IAAIM,GAAG,EAAE;QAClBN,OAAO,CAACe,MAAM,CAACT,GAAG,CAAC;;MAErB;MACAT,KAAK,CAACG,OAAO,CAACC,WAAW,CAACC,QAAQ,EAAE;;EAExC;EAEA;;;;;EAKQqL,uBAAuBA,CAACzL,UAAe;IAC7C,IAAI,CAACuB,YAAY,CAACsK,qBAAqB,CACrC,mBAAmB,EACnB,IAAI,CAACjN,oBAAoB,CAACkN,wBAAwB,CACnD;IACD9L,UAAU,CAACE,OAAO,CAACgL,cAAc,CAACa,eAAe,EAAE;IACnD,IAAI,CAACxK,YAAY,CAACyK,kBAAkB,CAClC,mBAAmB,EACnB,IAAI,CAACpN,oBAAoB,CAACkN,wBAAwB,CACnD;IACD,IAAI,CAACvK,YAAY,CAACU,KAAK,CAACgK,oBAAoB,CAACjM,UAAU,CAAC;EAC1D;EAEA;;;;;;;;EAQQkM,oBAAoBA,CAC1BC,EAAmB,EACnBC,GAAmB,EACnBC,KAAqB;IAErB,IAAI,CAACC,cAAc,CAACF,GAAG,EAAE,IAAI,CAAC;EAChC;EAEAG,eAAeA,CAAC1K,IAAY,EAAE2K,SAAiB;IAC7C,IAAI,CAACxN,iBAAiB,CAACyN,iBAAiB,CACtC5K,IAAI,EACJ2K,SAAS,EACT,IAAI,CAAC7M,kBAAkB,CACxB;EACH;EACA;;;;;;;;EAQQ+M,oBAAoBA,CAC1BP,EAAmB,EACnBC,GAAmB,EACnBO,KAAqB;IAErB,IAAI,CAACL,cAAc,CAACF,GAAG,EAAE,KAAK,CAAC;EACjC;EAEA;;;;;;EAMQQ,sBAAsBA,CAACC,IAAa;IAC1C,IAAI,CAAClO,eAAe,CAACmO,uBAAuB,CAACD,IAAI,CAAC;EACpD;EAEA;;;;;;;EAOQP,cAAcA,CAACF,GAAQ,EAAEW,IAAa;IAC5C,IAAI,CAACX,GAAG,EAAE,OAAO,KAAK;IACtB;IACA,MAAMY,IAAI,GAAGZ,GAAG,CAAClM,OAAO,CAAC2C,WAAW,CAACoK,YAAY;IACjDb,GAAG,CAACc,aAAa,GAAGH,IAAI,IAAIX,GAAG,CAACe,aAAa,CAACH,IAAI,CAACI,aAAa,CAAC;IACjE,OAAOhB,GAAG,CAACc,aAAa;EAC1B;EAEA;;;;;;;EAOQG,0BAA0BA,CAACR,IAAa,EAAEE,IAAa;IAC7DF,IAAI,CAACS,KAAK,CAAC/M,IAAI,CAAEgN,IAAoB,IAAI;MACvC,IAAIA,IAAI,CAAC7C,MAAM,KAAK,EAAE,EAAE;QACrB6C,IAAiB,CAACC,IAAI,GAAGT,IAAI,GAAG,oBAAoB,GAAG,IAAI;;IAEhE,CAAC,CAAC;IACF,IAAI,IAAI,CAACxL,YAAY,IAAI,IAAI,CAACA,YAAY,CAACsB,WAAW,CAAC4K,WAAW,EAAE;MAClE,IAAI,CAAClM,YAAY,CAACsB,WAAW,CAAC4K,WAAW,CAACC,iBAAiB,GAAG;QAC5DhN,QAAQ,EACNmM,IAAI,CAACpM,IAAI,CAACC,QAAQ,IAAIrC,gBAAgB,CAACkG,gBAAgB,GACnDlG,gBAAgB,CAACsP,UAAU,GAC3BtP,gBAAgB,CAACsC;OACxB;;EAEL;EAEA;;;;;EAKQ0J,gBAAgBA,CACtBuD,oBAAA,GAAsC,EAAE,EACxCC,aAAA,GAAyB,KAAK;IAE9B,MAAMC,YAAY,GAAiB,CACjC,IAAI/P,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAACC,UAAU,EAAE,EACxD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACvD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EAC/D,IAAI1K,EAAE,CAACyK,OAAO,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EAC7D,IAAI1K,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,OAAO,EAAGuF,KAAa,IAAI;MAChD,OAAO,IAAI,CAACvO,CAAC,CAACzB,EAAE,CAACiQ,KAAK,EAAE,QAAQ,EAAE;QAChC,GAAG,EAAE,IAAI,CAAC7O,iBAAiB,CAAC8O,0BAA0B,CAACF,KAAK,EAAE,GAAG,CAAC;QAClE,GAAG,EAAEA;OACN,CAAC;IACJ,CAAC,CAAC,CACH;IACD,IAAI,CAACF,aAAa,EAAE;MAClBC,YAAY,CAACpI,IAAI,CAAC,IAAI3H,EAAE,CAACyK,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAACC,UAAU,EAAE,CAAC;;IAEnE,OAAO,IAAI,CAACjJ,CAAC,CACXzB,EAAE,CAACmQ,KAAK,EACR,kBAAkB,EAClB;MACEC,MAAM,EAAE,SAAS;MACjB,GAAGP;KACJ,EACD,GAAGE,YAAY,CAChB;EACH;EAEA;;;;EAIQxD,iBAAiBA,CAAA;IACvB,OAAO,IAAI,CAAC9K,CAAC,CAACzB,EAAE,CAAC2I,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC0H,gBAAgB,EAAE,CAAC;EAC9D;EAEA;;;;EAIQA,gBAAgBA,CAAA;IACtB,OAAO,IAAI,CAAC5O,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,OAAO,EACP;MACE7E,IAAI,EAAE,OAAO;MACbwM,yBAAyB,EAAE,OAAO;MAClCC,8BAA8B,EAAE,CAAC;MACjC5D,MAAM,EAAE,EAAE;MACVyD,MAAM,EAAE,SAAS;MACjB5G,OAAO,EAAE,IAAIxJ,EAAE,CAACyJ,IAAI,CAAC,GAAG,EAAE,GAAG;KAC9B,EACD,IAAI,CAAC+G,0BAA0B,EAAE,EACjC,IAAI,CAACC,oBAAoB,EAAE,EAC3B,IAAI,CAACC,qBAAqB,EAAE,EAC5B,IAAI,CAACC,kBAAkB,EAAE,EACzB,IAAI3Q,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAACC,UAAU,EAAE,EACxD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAACC,UAAU,EAAE,CACnD;EACH;EAEA;;;;EAIQ8F,0BAA0BA,CAAA;IAChC,OAAO,CACL,IAAI,CAAC/O,CAAC,CAACzB,EAAE,CAAC4Q,mBAAmB,EAAE;MAC7BC,GAAG,EAAE,CAAC;MACNC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXhI,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACC,IAAI;MACxB+H,oBAAoB,EAAE,CAAC;MACvBC,eAAe,EAAE;KAClB,CAAC,EACF,IAAI,CAACxP,CAAC,CAACzB,EAAE,CAAC4Q,mBAAmB,EAAE;MAC7BC,GAAG,EAAE,CAAC;MACNC,OAAO,EAAE,EAAE;MACX/H,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACC,IAAI;MACxBnC,QAAQ,EAAE;KACX,CAAC,CACH;EACH;EAEA;;;;EAIQ2J,oBAAoBA,CAAA;IAC1B,OAAO,IAAI,CAAChP,CAAC,CACXzB,EAAE,CAACqJ,SAAS,EACZ;MACEwH,GAAG,EAAE,CAAC;MACNK,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE,sBAAsB;MAC5B3H,OAAO,EAAE,IAAIxJ,EAAE,CAACyJ,IAAI,CAAC,GAAG,EAAEC,GAAG,CAAC;MAC9BC,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjCN,WAAW,EAAE,KAAK;MAClBU,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;MACjBtB,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACzB6H,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAACU,MAAM;MAC9B8H,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,QAAQ;MACnBxN,IAAI,EAAE,WAAW;MACjBqG,UAAU,EAAE,IAAI,CAACoH,qBAAqB,CAACC,IAAI,CAAC,IAAI;KACjD,EACD,IAAIxR,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAACC,UAAU,EAAE,EAC3C,IAAI1K,EAAE,CAACyK,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,CACpD;EACH;EAEA;;;;;;EAMQ6G,qBAAqBA,CAC3BnH,SAAuB,EACvBqH,SAAiB,EACjBC,SAAiB;IAEjB,IAAI,CAACA,SAAS,CAACnH,IAAI,EAAE,EAAE;MACrB;MACAH,SAAS,CAACI,IAAI,GAAGiH,SAAS;KAC3B,MAAM;MACL,MAAME,SAAS,GAAGvH,SAAS,CAACwH,IAAI,EAAElP,IAAI;MACtC,IAAIiP,SAAS,CAAChP,QAAQ,KAAKrC,gBAAgB,CAACuL,OAAO,EAAE;QACnD,IAAI,CAACxK,kBAAkB,CAACwQ,aAAa,CACnCF,SAAS,CAAC7N,IAAI,EACd4N,SAAS,EACTC,SAAS,EACT,IAAI,CAACnO,YAAY,CAClB;OACF,MAAM;QACL,IAAI,CAAC5C,eAAe,CAACmO,uBAAuB,CAAC3E,SAAS,CAACwH,IAAK,CAAC;;MAE/D,IACED,SAAS,CAAChP,QAAQ,KAAKrC,gBAAgB,CAACiG,KAAK,IAC7CoL,SAAS,CAAChP,QAAQ,KAAKrC,gBAAgB,CAACkG,gBAAgB,EACxD;QACA,IAAI,CAAC1F,gBAAgB,CAACgR,mBAAmB,CACvC;UAAE,GAAGH,SAAS;UAAE7N,IAAI,EAAE4N;QAAS,CAAE,EACjC,IAAI,CAAClO,YAAY,CAClB;;MAEH,IAAImO,SAAS,CAAChP,QAAQ,KAAKrC,gBAAgB,CAACmG,WAAW,EAAE;QACvD,IAAI,CAAC1F,eAAe,CAACgR,4BAA4B,CAC/C;UAAE,GAAGJ,SAAS;UAAE7N,IAAI,EAAE4N;QAAS,CAAE,EACjC,IAAI,CAAClO,YAAY,CAClB;;;EAGP;EAEA;;;;EAIQkN,qBAAqBA,CAAA;IAC3B,OAAO,IAAI,CAACjP,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,UAAU,EACV;MACE7E,IAAI,EAAE,YAAY;MAClB+M,GAAG,EAAE,CAAC;MACN9H,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACC,IAAI;MACxBL,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACzByI,YAAY,EAAE,IAAI,CAAC5J,sBAAsB;KAC1C,EACD,IAAIpI,EAAE,CAACyK,OAAO,CAAC,WAAW,EAAE,OAAO,EAAGwH,KAAK,IACzCA,KAAK,CAACC,MAAM,CACTC,IAAuD,IACtDA,IAAI,CAACxP,QAAQ,IAAIrC,gBAAgB,CAACsN,kBAAkB,IACpDuE,IAAI,CAACxP,QAAQ,IAAIrC,gBAAgB,CAACsK,SAAS,CAC9C,CACF,EACD,IAAI5K,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,OAAO,EAAGwH,KAAK,IACvCA,KAAK,CAACG,IAAI,CACPD,IAAuD,IACtDA,IAAI,CAACxP,QAAQ,KAAKrC,gBAAgB,CAACsK,SAAS,IAC5CuH,IAAI,CAACxP,QAAQ,KAAKrC,gBAAgB,CAACsN,kBAAkB,CACxD,CACF,CACF;EACH;EAEA;;;;EAIQ+C,kBAAkBA,CAAA;IACxB,OAAO,IAAI,CAAClP,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,UAAU,EACV;MACE7E,IAAI,EAAE,SAAS;MACf+M,GAAG,EAAE,CAAC;MACN9H,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACC,IAAI;MACxBL,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACzByI,YAAY,EAAE,IAAI,CAAC5J,sBAAsB;KAC1C,EACD,IAAIpI,EAAE,CAACyK,OAAO,CAAC,WAAW,EAAE,OAAO,EAAGwH,KAAK,IACzCA,KAAK,CAACC,MAAM,CACTC,IAA2D,IAC1DA,IAAI,CAACxP,QAAQ,IAAIrC,gBAAgB,CAACuK,SAAS,CAC9C,CACF,EACD,IAAI7K,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,OAAO,EAAGwH,KAAK,IACvCA,KAAK,CAACG,IAAI,CACPD,IAA2D,IAC1DA,IAAI,CAACxP,QAAQ,KAAKrC,gBAAgB,CAACuK,SAAS,CAC/C,CACF,CACF;EACH;EAEA;;;;;;;;;EASQ6B,UAAUA,CAChBC,MAAc,EACd/D,SAAkB,EAClByJ,QAAiB,EACjBC,OAAgB;IAEhB,OAAO,IAAI,CAAC7Q,CAAC,CAACzB,EAAE,CAACmQ,KAAK,EAAE;MACtBoC,MAAM,EAAE,QAAQ;MAChB9C,IAAI,EAAE,aAAa;MACnB9E,MAAM,EAAE,IAAI;MACZ6H,WAAW,EAAE,IAAIxS,EAAE,CAACyJ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9Bb,SAAS,EAAEA,SAAS;MACpBwI,cAAc,EAAExI,SAAS;MACzB+D,MAAM,EAAEA,MAAM;MACd8F,QAAQ,EAAE7J,SAAS;MACnB8J,MAAM,EAAE9J,SAAS;MACjB+J,YAAY,EAAEN,QAAQ;MACtBO,UAAU,EAAEN,OAAO;MACnBO,oBAAoB,EAAER,QAAQ;MAC9BS,kBAAkB,EAAER,OAAO;MAC3BlC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;;;;;;EAMQzE,0BAA0BA,CAAA;IAChC,OAAO,IAAI,CAAClK,CAAC,CACXzB,EAAE,CAACmM,KAAK,EACR,MAAM,EACN;MACE4G,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAEA,CAAC7E,EAAE,EAAEC,GAAG,KAAK,IAAI,CAACE,cAAc,CAACF,GAAG,EAAE,IAAI,CAAC;MAC3D6E,cAAc,EAAEA,CAAC9E,EAAE,EAAEC,GAAG,KAAK,IAAI,CAACE,cAAc,CAACF,GAAG,EAAE,KAAK,CAAC;MAC5D8E,uBAAuB,EAAE,IAAI;MAC7BC,+BAA+B,EAAE,IAAI;MACrCC,SAAS,EAAE,IAAI,CAACtR,oBAAoB;MACpCuR,yBAAyB,EAAE,IAAI;MAC/BC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;KACnB,EACD,IAAI,CAACC,uBAAuB,EAAE,EAC9B,IAAI,CAACC,wBAAwB,EAAE,CAChC;EACH;EAEA;;;;;;EAMQD,uBAAuBA,CAAA;IAC7B,OAAO,IAAI,CAAChS,CAAC,CACXzB,EAAE,CAACmQ,KAAK,EACR,kBAAkB,EAClB;MACExF,MAAM,EAAE,IAAI,CAACgJ,YAAY,CAAC,IAAI,CAAC;MAC/BlE,IAAI,EAAE,IAAI,CAACkE,YAAY,CAAC,IAAI,CAAC;MAC7BC,WAAW,EAAE;KACd,EACD,IAAI5T,EAAE,CAACyK,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAACkJ,YAAY,CAAC,EACpD,IAAI3T,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAACkJ,YAAY,CAAC,CACnD;EACH;EAEA;;;;;;EAMQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACjS,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,UAAU,EACV;MAAE7E,IAAI,EAAE;IAAa,CAAE,EACvB,IAAI,CAAC+P,wBAAwB,EAAE,EAC/B,IAAI,CAACpS,CAAC,CAACzB,EAAE,CAAC8T,WAAW,EAAE;MACrBC,OAAO,EAAE,CAAC;MACVnL,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACmL,UAAU;MAC7BxK,OAAO,EAAE,IAAIxJ,EAAE,CAACyJ,IAAI,CAAC,GAAG,EAAE,GAAG;KAC9B,CAAC,CACH;EACH;EAEQwK,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACxS,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,UAAU,EACV;MAAE7E,IAAI,EAAE;IAAa,CAAE,EACvB,IAAI,CAAC+P,wBAAwB,EAAE,EAC/B,IAAI,CAACpS,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACE8H,IAAI,EAAE,wCAAwC;MAC9CxH,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAClC0H,SAAS,EAAE,MAAM;MACjB4C,QAAQ,EAAEC,QAAQ;MAClB3K,OAAO,EAAE,IAAIxJ,EAAE,CAACyJ,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;MAC9BM,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;MACjB;MACAtB,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACC,OAAO;MAC1Be,QAAQ,EAAE7J,EAAE,CAAC8J,YAAY,CAACsK,IAAI;MAC9BrL,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACC,IAAI;MACxBkB,UAAU,EAAE,IAAI,CAACoH,qBAAqB,CAACC,IAAI,CAAC,IAAI,CAAC;MACjD6B,SAAS,EAAEA,CAACgB,CAAgB,EAAEC,IAAoB,KAChDD,CAAC,CAAClS,OAAO,CAACC,WAAW,CAACC,QAAQ;KACjC,EACD,IAAIrC,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,aAAa,CAAC,CAACC,UAAU,EAAE,EAClD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACnD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAACC,UAAU,EAAE,CACnD,CACF;EACH;EAEA;;;;;;EAMQmJ,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACpS,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,OAAO,EACP;MACEI,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACuL,UAAU;MAC9BxB,UAAU,EAAE,IAAI,CAACY,YAAY,CAAC,IAAI;KACnC,EACD,IAAI3T,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAACkJ,YAAY,CAAC,EACxD,IAAI,CAAClS,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACET,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAAC2L,IAAI;MACvBzL,OAAO,EAAE/I,EAAE,CAACgJ,OAAO,CAACuL,UAAU;MAC9BlD,QAAQ,EAAE,IAAI;MACd/H,WAAW,EAAE,KAAK;MAClBU,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;MACjBP,MAAM,EAAE,CAAC;MACTwH,IAAI,EAAE,IAAI,CAACsD,WAAW,CAAC,KAAK,CAAC;MAC7BC,OAAO,EAAE,IAAI;MACb/J,MAAM,EAAE,SAAS;MACjBR,UAAU,EAAE,IAAI,CAACwK,gBAAgB,CAACnD,IAAI,CAAC,IAAI;KAC5C,EACD,IAAIxR,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAACgK,WAAW,CAAC,EACjD,IAAIzU,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAACC,UAAU,EAAE,CAC5C,CACF;EACH;EAEQiK,gBAAgBA,CACtBvK,SAAuB,EACvBwK,UAAkB,EAClBlD,SAAiB;IAEjB,MAAMC,SAAS,GACbvH,SAAS,CAACjI,OAAO,EAAEI,SAAS,CAACsS,KAAK,EAAE,EAAEnS,IAAI;IAC5C,IAAIiP,SAAS,CAAChP,QAAQ,KAAKrC,gBAAgB,CAACuL,OAAO,EAAE;MACnD,IAAI,CAACxK,kBAAkB,CAACwQ,aAAa,CACnCH,SAAS,EACTC,SAAS,CAACmD,WAAW,EACrBnD,SAAS,EACT,IAAI,CAACnO,YAAY,CAClB;;EAEL;EAEA;;;;;;;EAOQmQ,YAAYA,CAACoB,KAAc;IACjC;IACA,OAAOA,KAAK,GAAG,0BAA0B,GAAG,uBAAuB;EACrE;EAEA;;;;;;EAMQN,WAAWA,CAACM,KAAc;IAChC;IACA,OAAOA,KAAK,GAAG,sBAAsB,GAAG,sBAAsB;EAChE;EAEQnJ,8BAA8BA,CAAA;IACpC,OAAO,IAAI,CAACnK,CAAC,CACXzB,EAAE,CAACmM,KAAK,EACR,MAAM,EACN;MACE6I,0BAA0B,EAAE,IAAI,CAACvT,CAAC,CAChCzB,EAAE,CAACiV,SAAS,EACZ,MAAM,EACN,IAAI,CAACC,0BAA0B,EAAE,EACjC,IAAI,CAACC,uBAAuB,CAAC,KAAK,CAAC,CACpC;MACD,GAAG,IAAI,CAACC,wBAAwB;KACjC;IACD;IACA,GAAG,IAAI,CAAC/I,iBAAiB,EAAE,EAC3B,IAAI,CAACC,gBAAgB,EAAE,EACvB,IAAI,CAACC,iBAAiB,EAAE,CACzB;EACH;EAEA;;;;;EAKQ8I,qBAAqBA,CAACC,YAAqB;IACjD,OAAO,IAAI,CAACC,kBAAkB,CAC5B,CAACD,YAAY,GAAGjV,YAAY,CAACuK,SAAS,GAAGvK,YAAY,CAACuN,kBAAkB,EACxE,WAAW,EACX,CAACQ,EAAE,EAAE3L,GAAG,KACN6S,YAAY,GACR,IAAI,CAACE,cAAc,CAAC/S,GAAG,CAAC,GACxB,IAAI,CAACvB,oBAAoB,CAACuU,gBAAgB,CACxChT,GAAG,EACH,WAAW,EACXnC,gBAAgB,CAACsK,SAAS,EAC1B,IAAI,CAACpH,YAAY,CAClB,CACR;EACH;EAEA;;;;;;;;EAQQ+R,kBAAkBA,CACxBG,WAAmB;EAAE;EACrBC,WAAmB;EAAE;EACrBC,YAA6D;IAE7D,OAAO,IAAI,CAACnU,CAAC,CACX,QAAQ,EACR;MACEoU,KAAK,EAAED,YAAY;MACnBjM,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAClCkM,OAAO,EAAE,IAAI,CAACrU,CAAC,CACbzB,EAAE,CAACiV,SAAS;MAAE;MACd,MAAM;MAAE;MACR;QACErM,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACkN,GAAG;QACtB3E,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAACmN,MAAM,CAAE;OACjC,EACD,IAAI,CAACvU,CAAC,CACJzB,EAAE,CAACmQ,KAAK,EACR;QAAEV,IAAI,EAAE,OAAO;QAAE9E,MAAM,EAAE;MAAI,CAAE,CAAC;OACjC,EACD,IAAI,CAAClJ,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;QACE8H,IAAI,EAAE,iBAAiB;QACvBxH,MAAM,EAAE,CAAC;QACT2H,SAAS,EAAE,QAAQ;QACnBtH,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;QACjBtB,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACkN,GAAG;QACtB3E,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAACkN,GAAG,CAAE;OAC9B,EACDJ,WAAW,CAAC;OACb;KAEJ,EACD,IAAI,CAAClU,CAAC,CACJzB,EAAE,CAACqJ,SAAS;IAAE;IACd;MACE8H,IAAI,EAAE,kBAAkB;MACxB3G,IAAI,EAAEkL,WAAW;MACjBpE,SAAS,EAAE,QAAQ;MACnB2E,iBAAiB,EAAEjW,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACjCiJ,WAAW,EAAE,IAAIxS,EAAE,CAACyJ,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAE;KACnC,CACF,CACF;EACH;EAEA;;;;;;;;;EASQ+L,cAAcA,CAAC/S,GAAmB;IACxC;IACA,MAAMyT,YAAY,GAAGzT,GAAG,CAACN,OAAQ,CAACI,SAAS,CAACsS,KAAK,EAAG;IACpD;IACA,MAAMsB,qBAAqB,GAAGD,YAAY,CAACxT,IAAI,CAACyT,qBAAqB;IACrE;IACA,IAAI,CAACnV,kBAAkB,CAACoV,eAAe,CACrCF,YAAY,CAACxT,IAAI,EACjB,SAAS,EACTyT,qBAAqB,EACrB,IAAI,CAAC3S,YAAY,CAClB;EACH;EAEA;;;;;;;EAOQ8E,qBAAqBA,CAAA;IAC3B,IAAI,CAAC9E,YAAY,CAAC6S,YAAY,GAAG,IAAI,CAACC,kBAAkB,EAAE;IAC1D,IAAI,CAAC9S,YAAY,CAAC+S,eAAe,CAAC9K,GAAG,CACnCnL,gBAAgB,CAACsP,UAAU,EAC3B,IAAI,CAAC4G,wBAAwB,EAAE,CAChC;EACH;EAEA;;;;;;;EAOQF,kBAAkBA,CAAA;IACxB,OAAO,IAAI,CAAC7U,CAAC,CACXzB,EAAE,CAACyW,IAAI,EACP;MACEC,gBAAgB,EAAE,IAAI,CAAC7H,sBAAsB,CAAC2C,IAAI,CAAC,IAAI;KACxD,EACD,IAAI,CAACmF,qBAAqB,EAAE,EAC5B,IAAI3W,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACxD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACzD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACvD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACpD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE;IAC/D;IACA,IAAI1K,EAAE,CAACyK,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACvD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EAC/D,IAAI1K,EAAE,CAACyK,OAAO,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EAC7D,IAAI,CAACkM,eAAe,EAAE,EACtB,IAAI,CAACC,mBAAmB,CAAC,MAAM,CAAC,EAChC,IAAI,CAACC,0BAA0B,CAC7B,cAAc,EACd,CAAC,EACD,GAAG,EACH,IAAI9W,EAAE,CAACsG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EACrB,eAAe,CAChB,EACD,IAAI,CAACwQ,0BAA0B,CAC7B,cAAc,EACd,CAAC,CAAC,EACF,GAAG,EACH,IAAI9W,EAAE,CAACsG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EACtB,iBAAiB,CAClB,EACD,IAAI,CAAC7E,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACE;MACA0N,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,IAAIhX,EAAE,CAACsG,KAAK,CAACoD,GAAG,EAAE,EAAE,CAAC;MACpC2H,QAAQ,EAAE,IAAI;MACd4F,OAAO,EAAE,IAAIjX,EAAE,CAACyJ,IAAI,CAAC,GAAG,EAAEC,GAAG,CAAC;MAC9BG,QAAQ,EAAE7J,EAAE,CAAC8J,YAAY,CAACC,QAAQ;MAClCC,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;MACjBZ,WAAW,EAAE;KACd,EACD,IAAItJ,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,aAAa,CAAC,CAACC,UAAU,EAAE,CACnD,EACD,IAAI,CAACjJ,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACE;MACA0N,YAAY,EAAE,CAAC,CAAC;MAChBC,aAAa,EAAE,IAAIhX,EAAE,CAACsG,KAAK,CAACoD,GAAG,EAAE,EAAE,CAAC;MACpC2H,QAAQ,EAAE,IAAI;MACd4F,OAAO,EAAE,IAAIjX,EAAE,CAACyJ,IAAI,CAAC,GAAG,EAAEC,GAAG,CAAC;MAC9BG,QAAQ,EAAE7J,EAAE,CAAC8J,YAAY,CAACC,QAAQ;MAClCC,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;MACjBZ,WAAW,EAAE;KACd,EACD,IAAItJ,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAACC,UAAU,EAAE,CACjD,CACF;EACH;EAEQ8L,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAAC/U,CAAC,CACXzB,EAAE,CAACyW,IAAI,EACP;MACES,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;KACV,EACD,IAAInX,EAAE,CAACyK,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,EAC5C,IAAIzK,EAAE,CAACyK,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,EAC1C,IAAIzK,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,EACxC,IAAIzK,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,EACxC,IAAI,CAAChJ,CAAC,CAACzB,EAAE,CAACmQ,KAAK,EAAE;MACfV,IAAI,EAAE,OAAO;MACbmE,WAAW,EAAE,CAAC;MACdwD,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC;KACvB,CAAC,CACH;EACH;EACQP,mBAAmBA,CAACQ,WAAmB;IAC7C,OAAO,IAAI,CAAC5V,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,MAAM,EACN,IAAI,CAAClH,CAAC,CAACzB,EAAE,CAACmQ,KAAK,EAAE;MACfV,IAAI,EAAE,IAAI,CAAChO,CAAC,CAACzB,EAAE,CAACiQ,KAAK,EAAE,QAAQ,CAAC;MAChCtF,MAAM,EAAE,aAAa;MACrBoI,UAAU,EAAE;KACb,CAAC,EACF,IAAI,CAACtR,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACEiI,SAAS,EAAE,QAAQ;MACnBH,IAAI,EAAE,mCAAmC;MACzCxG,MAAM,EAAE,OAAO;MACfhB,MAAM,EAAE,CAAC;MACTL,WAAW,EAAE,KAAK;MAClB+H,QAAQ,EAAE;KACX,EACD,IAAIrR,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE4M,WAAW,CAAC,CAAC3M,UAAU,EAAE,EAChD,IAAI1K,EAAE,CAACyK,OAAO,CACZ,iBAAiB,EACjB,+BAA+B,CAChC,CAACC,UAAU,EAAE,CACf,EACD,IAAI1K,EAAE,CAACyK,OAAO,CACZ,eAAe,EACf,eAAe,EACfzK,EAAE,CAACsG,KAAK,CAACP,KAAK,CACf,CAAC2E,UAAU,CAAC1K,EAAE,CAACsG,KAAK,CAACS,SAAS,CAAC,CACjC;EACH;EACA;;;;;;EAMQ4P,qBAAqBA,CAAA;IAC3B,OAAO;MACLW,OAAO,EAAEtX,EAAE,CAACuX,OAAO,CAACC,WAAW;MAC/BC,MAAM,EAAE,CAAC;MACTC,oBAAoB,EAAE,EAAE;MACxBC,kBAAkB,EAAE,EAAE;MACtBC,KAAK,EAAE5X,EAAE,CAAC6X,KAAK,CAACC,QAAQ;MACxBC,aAAa,EAAE;KACL;EACd;EAEA;;;;;EAKQnB,eAAeA,CAAA;IACrB,OAAO,IAAI,CAACnV,CAAC,CACXzB,EAAE,CAACmQ,KAAK,EACR;MACE6H,eAAe,EAAE,EAAE;MACnBC,KAAK,EAAE,GAAG;MACVrE,WAAW,EAAE,GAAG,CAAE;KACnB;IACD;IACA,IAAI5T,EAAE,CAACyK,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAACC,UAAU,EAAE,EAC9C,IAAI1K,EAAE,CAACyK,OAAO,CAAC,aAAa,EAAE,eAAe,EAAGyN,CAAC,IAC/CA,CAAC,GAAG,GAAG,GAAG,GAAG,CACd,CAACC,QAAQ,EAAE,CACb;EACH;EAEA;;;;;;;;;;EAUQrB,0BAA0BA,CAChChT,IAAY,EACZiT,YAAoB,EACpBiB,eAAuB,EACvBhB,aAAuB,EACvBoB,eAAuB;IAEvB,OAAO,IAAI,CAAC3W,CAAC,CACXzB,EAAE,CAACqJ,SAAS,EACZ;MACEvF,IAAI,EAAEA,IAAI;MACViT,YAAY,EAAEA,YAAY;MAC1BiB,eAAe,EAAEA,eAAe;MAChChB,aAAa,EAAEA,aAAa;MAC5BlM,UAAU,EAAEC,MAAM,CAACC,mBAAmB;MACtCqN,OAAO,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;KACnC,EACD,IAAIrY,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE2N,eAAe,CAAC,CAAC1N,UAAU,EAAE,EACpD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAACC,UAAU,EAAE,EACjD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,CACpD;EACH;EAEA;;;;;;;;EAQQnC,+BAA+BA,CAAA;IACrC,IAAI,CAAC/E,YAAY,CAAC8H,aAAa,CAAC0J,0BAA0B,GAAG,IAAI,CAACvT,CAAC,CACjEzB,EAAE,CAACiV,SAAS,EACZ,MAAM,EACN,IAAI,CAACC,0BAA0B,EAAE,EACjC,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,CAC1C;EACH;EAEA;;;;;;;;EAQQD,0BAA0BA,CAAA;IAChC,OAAO,IAAI,CAACzT,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,MAAM,EACN,IAAI,CAAClH,CAAC,CAACzB,EAAE,CAACmQ,KAAK,EAAE;MAAExF,MAAM,EAAE,YAAY;MAAEiJ,WAAW,EAAE,CAAC;MAAEnE,IAAI,EAAE;IAAI,CAAE,CAAC,EACtE,IAAI,CAAChO,CAAC,CAACzB,EAAE,CAAC8T,WAAW,CAAC,CACvB;EACH;EAEA;;;;;;EAMQqB,uBAAuBA,CAC7BmD,OAAgB,EAChBxI,aAAA,GAAyB,KAAK;IAE9B,MAAMyI,YAAY,GAAe,EAAE;IACnC,IAAID,OAAO,EAAE;MACXC,YAAY,CAAC5Q,IAAI,CAAC,IAAI,CAAC0N,qBAAqB,CAAC,KAAK,CAAC,CAAC;MACpDkD,YAAY,CAAC5Q,IAAI,CAAC,IAAI,CAAC6Q,kBAAkB,EAAE,CAAC;MAC5C,IAAI,CAAC1I,aAAa,EAAEyI,YAAY,CAAC5Q,IAAI,CAAC,IAAI,CAAC8Q,gBAAgB,EAAE,CAAC;KAC/D,MAAM;MACLF,YAAY,CAAC5Q,IAAI,CAAC,IAAI,CAAC0N,qBAAqB,CAAC,IAAI,CAAC,CAAC;;IAErD,OAAO,IAAI,CAAC5T,CAAC,CACXzB,EAAE,CAAC2I,KAAK,EACR,YAAY,EACZ;MACEC,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACkN,GAAG;MACtB3E,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAACmN;KACzB,EACD,GAAGuC,YAAY,CAChB;EACH;EAEA;;;;EAIQC,kBAAkBA,CAAA;IACxB,OAAO,IAAI,CAACjD,kBAAkB,CAC5BlV,YAAY,CAACwK,SAAS,EACtB,QAAQ,EACR,CAACuD,EAAE,EAAE3L,GAAG,KACN,IAAI,CAACvB,oBAAoB,CAACuU,gBAAgB,CACxChT,GAAG,EACH,QAAQ,EACRnC,gBAAgB,CAACuK,SAAS,EAC1B,IAAI,CAACrH,YAAY,CAClB,CACJ;EACH;EAEA;;;;;EAKQiV,gBAAgBA,CAAA;IACtB,OAAO,IAAI,CAAChX,CAAC,CACX,QAAQ,EACR;MACEoU,KAAK,EAAEA,CAACxB,CAAC,EAAE5R,GAAG,KAAK,IAAI,CAACiW,mBAAmB,CAACrE,CAAC,EAAE5R,GAAG,CAAC;MACnDkW,UAAU,EAAEA,CAACtE,CAAC,EAAE5R,GAAG,KAAK,IAAI,CAACiW,mBAAmB,CAACrE,CAAC,EAAE5R,GAAG,CAAC;MACxDqT,OAAO,EAAE,IAAI,CAACrU,CAAC,CACbzB,EAAE,CAACiV,SAAS;MAAE;MACd,MAAM;MAAE;MACR;QACErM,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACkN,GAAG;QACtB3E,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAACmN,MAAM,CAAE;OACjC,EACD,IAAI,CAACvU,CAAC,CACJzB,EAAE,CAACmQ,KAAK,EACR;QAAEV,IAAI,EAAE,OAAO;QAAE9E,MAAM,EAAE;MAAI,CAAE,CAAC;OACjC,EACD,IAAI,CAAClJ,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;QACE8H,IAAI,EAAE,iBAAiB;QACvBxH,MAAM,EAAE,CAAC;QACT2H,SAAS,EAAE,QAAQ;QACnBtH,IAAI,EAAEhK,EAAE,CAACiK,IAAI,CAACC,GAAG;QACjBtB,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAACkN,GAAG;QACtB3E,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAACkN,GAAG,CAAE;OAC9B,EACD,WAAW,CAAC;OACb;KAEJ,EACD,IAAI,CAACtU,CAAC,CAACzB,EAAE,CAACmQ,KAAK,EAAE;MACfyI,cAAc,EAAE,0CAA0C;MAC1DpG,WAAW,EAAE,IAAIxS,EAAE,CAACyJ,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;MAChCgG,IAAI,EAAE;KACP,CAAC,CACH;EACH;EAEA;;;;;EAKQiJ,mBAAmBA,CAAC1W,KAAoB,EAAE6W,MAAW;IAC3D,MAAM3C,YAAY,GAAG2C,MAAM,CAACjH,IAAI,CAACkH,WAAW;IAC5C,MAAMpJ,WAAW,GAAG1N,KAAK,CAACG,OAAO,CAAC2C,WAAW,CAAC4K,WAAW;IACzD,MAAMqJ,YAAY,GAAG7C,YAAY,CAAC8C,QAAQ,CAAC,IAAI,CAAC;IAChD,IAAID,YAAY,EAAE;MAChBrJ,WAAW,CAACuJ,WAAW,GAAGF,YAAY;MACtC/W,KAAK,CAACG,OAAO,CAACC,WAAW,GAAGsN,WAAW;MACvCA,WAAW,CAACwJ,UAAU,EAAE;KACzB,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,qCAAqC,CAAC;;EAExD;EAEA;;;;;;EAMQ5Q,iBAAiBA,CAAA;IACvB,IAAI,CAAC7H,YAAY,CAAC0Y,0BAA0B,CAAC,IAAI,CAAC7V,YAAY,CAAC;EACjE;EAEQsI,0BAA0BA,CAAA;IAChC,OAAO,IAAI,CAACrK,CAAC,CACXzB,EAAE,CAACmM,KAAK,EACR,MAAM,EACN;MACE4G,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,IAAI;MACjBG,uBAAuB,EAAE,IAAI;MAC7BC,+BAA+B,EAAE,IAAI;MACrCkG,kBAAkB,EAAE,KAAK;MACzBhG,yBAAyB,EAAE,IAAI;MAC/BC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;KACnB;IACD;IACA,GAAG,IAAI,CAACnH,iBAAiB,EAAE,EAC3B,IAAIrM,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,eAAe,EAAGyN,CAAC,IAC9CA,CAAC,GAAG,mBAAmB,GAAG,aAAa,CACxC,CAACC,QAAQ,EAAE,EACZ,IAAI,CAAC1E,uBAAuB,EAAE,EAC9B,IAAI,CAACQ,wBAAwB,EAAE,CAChC;EACH;EAEA;;;;;;;EAOQlM,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACtG,CAAC,CACXzB,EAAE,CAACuZ,IAAI,EACP,YAAY,EACZ,IAAI,CAAC9X,CAAC,CACJzB,EAAE,CAACqJ,SAAS;IAAE;IACd;MACE4O,KAAK,EAAE,EAAE;MACTuB,MAAM,EAAE,EAAE;MACVrI,IAAI,EAAE,kBAAkB;MACxBG,SAAS,EAAE,QAAQ;MACnB2E,iBAAiB,EAAEjW,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACjCI,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;KACpC,EACD,IAAI5J,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAChC,EACD,IAAI,CAAChJ,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACEsB,MAAM,EAAE,OAAO;MACfwG,IAAI,EAAE,iBAAiB;MACvBE,QAAQ,EAAE,IAAI;MACd/H,WAAW,EAAE,KAAK;MAClB8G,MAAM,EAAE,SAAS;MACjBzD,MAAM,EAAE;KACT,EACD,IAAI3M,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAC/B,CACF;EACH;EAEA;;;;;;;EAOQxC,yBAAyBA,CAAA;IAC/B,OAAO,IAAI,CAACxG,CAAC,CACXzB,EAAE,CAACmM,KAAK,EACR,YAAY,EACZ;MACEiE,MAAM,EAAE;KACT,EACD,IAAIpQ,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI,CAACjJ,CAAC,CACJzB,EAAE,CAACqJ,SAAS;IAAE;IACd;MACEmQ,MAAM,EAAE,EAAE;MACVvB,KAAK,EAAE,EAAE;MACT9G,IAAI,EAAE,kBAAkB;MACxBG,SAAS,EAAE,QAAQ;MACnB2E,iBAAiB,EAAEjW,EAAE,CAAC6I,IAAI,CAACU,MAAM;MACjCI,MAAM,EAAE,IAAI3J,EAAE,CAAC4J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;KACpC,EACD,IAAI5J,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAChC,EACD,IAAI,CAAChJ,CAAC,CACJzB,EAAE,CAACqJ,SAAS,EACZ;MACEvF,IAAI,EAAE,OAAO;MACb6G,MAAM,EAAE,OAAO;MACfwG,IAAI,EAAE,iBAAiB;MACvBvI,SAAS,EAAE5I,EAAE,CAAC6I,IAAI,CAAC4Q,KAAK;MACxBrI,cAAc,EAAEpR,EAAE,CAAC6I,IAAI,CAAC2L;KACzB,EACD,IAAIxU,EAAE,CAACyK,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAC/B,CACF;EACH;EAEQsB,mCAAmCA,CAAA;IACzC,OAAO,IAAI,CAACtK,CAAC,CACXzB,EAAE,CAACmM,KAAK,EACR,MAAM,EACN;MACE6I,0BAA0B,EAAE,IAAI,CAACvT,CAAC,CAChCzB,EAAE,CAACiV,SAAS,EACZ,MAAM,EACN,IAAI,CAACC,0BAA0B,EAAE,EACjC,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CACzC;MACD,GAAG,IAAI,CAAC/I,+BAA+B;KACxC;IACD;IACA,GAAG,IAAI,CAACC,iBAAiB,EAAE,EAC3B,IAAI,CAACC,gBAAgB,CACnB;MACE3B,MAAM,EAAE,OAAO;MACfiJ,WAAW,EAAE,GAAG;MAChBwD,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC;KACvB,EACD,IAAI,CACL,EACD,IAAI,CAAC7K,iBAAiB,EAAE,EACxB,GAAGrM,YAAY,CAACsM,GAAG,CAAEC,QAAQ,IAC3B,IAAI,CAACC,UAAU,CACbD,QAAQ,CAACE,MAAM,EACfF,QAAQ,CAAC7D,SAAS,EAClB6D,QAAQ,CAACG,cAAc,EACvBH,QAAQ,CAACI,YAAY,CACtB,CACF,CACF;EACH;EAEQX,uBAAuBA,CAAA;IAC7B,OAAO,IAAI,CAACzK,CAAC,CACXzB,EAAE,CAACuZ,IAAI,EACP;MACEG,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,YAAY;MACvBxC,OAAO,EAAE,KAAK;MACdD,SAAS,EAAE,KAAK;MAChBrE,oBAAoB,EAAE;KACvB,EACD,IAAI7S,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI,CAACjJ,CAAC,CACJzB,EAAE,CAACmQ,KAAK,EACR,SAAS,EACT;MACE8H,KAAK,EAAE,GAAG;MACVuB,MAAM,EAAE,GAAG;MACX/J,IAAI,EAAE,kBAAkB;MACxB9E,MAAM,EAAE,kBAAkB;MAC1BgC,MAAM,EAAE,EAAE;MACVyD,MAAM,EAAE,SAAS;MACjBuC,YAAY,EAAE;KACf,EACD,IAAI3S,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,CACtD,CACF;EACH;EAEA;;;;;;EAMQ0K,wBAAwBA,CAAA;IAC9B,OAAO;MACLnC,cAAc,EAAEA,CAAA,KAAM,IAAI,CAAC9E,oBAAoB,CAACqD,IAAI,CAAC,IAAI,CAAC;MAC1D0B,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACvE,oBAAoB,CAAC6C,IAAI,CAAC,IAAI,CAAC;MAC1D2B,uBAAuB,EAAE,IAAI;MAC7BC,+BAA+B,EAAE,IAAI;MACrCC,SAAS,EAAE,IAAI,CAACtR,oBAAoB;MACpCyR,gBAAgB,EAAE,OAAO;MACzBkD,gBAAgB,EAAE,IAAI,CAAC7H,sBAAsB,CAAC2C,IAAI,CAAC,IAAI;KACxD;EACH;EAEA;;;;;;EAMQpF,+BAA+BA,CAAA;IACrC,OAAO;MACL,GAAG,IAAI,CAACgJ,wBAAwB,EAAE;MAClCwE,UAAU,EAAEA,CAACxL,EAAc,EAAEU,IAAa,KACxC,IAAI,CAACQ,0BAA0B,CAACR,IAAI,EAAE,IAAI,CAAC;MAC7C+K,UAAU,EAAEA,CAACzL,EAAc,EAAEU,IAAa,KACxC,IAAI,CAACQ,0BAA0B,CAACR,IAAI,EAAE,KAAK,CAAC;MAC9CgL,cAAc,EAAEA,CACdC,QAAiB,EACjBC,SAAyB,EACzBC,MAAe,EACfC,OAAuB,EACvBC,IAAa,KAEb,IAAI,CAAChZ,sBAAsB,CAACiZ,iBAAiB,CAC3CL,QAAQ,EACRE,MAAM,EACNE,IAAI,EACJH,SAAS,CAACrN,MAAM,EAChB,IAAI,CAACnJ,YAAY;KAEtB;EACH;EAEA;;;;;;EAMQ6I,iBAAiBA,CAAA;IACvB,OAAO,CACL,IAAIrM,EAAE,CAACyK,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE;IACpD;IACA,IAAI1K,EAAE,CAACyK,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACrD,IAAI1K,EAAE,CAACyK,OAAO,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAACC,UAAU,EAAE,EACpE,IAAI1K,EAAE,CAACyK,OAAO,CAAC,UAAU,EAAE,UAAU,EAAEzK,EAAE,CAACsG,KAAK,CAACP,KAAK,CAAC,CAAC2E,UAAU,CAC/D1K,EAAE,CAACsG,KAAK,CAACS,SAAS,CACnB,CACF;EACH;EAEA;;;;;;;EAOQmB,yBAAyBA,CAC/BmS,eAAwB,EACxB/O,aAAuB;IAEvBnL,cAAc,CAACkH,OAAO,CAAEiT,MAAM,IAAI;MAChC,IAAI,CAACta,EAAE,CAACua,OAAO,CAACC,OAAO,CAACF,MAAM,CAACxW,IAAI,CAAC,EAAE;QACpC,MAAM2W,OAAO,GAAG,IAAIza,EAAE,CAACua,OAAO,CAACD,MAAM,CAACxW,IAAI,CAAC;QAE3C;QACA2W,OAAO,CAACC,SAAS,GAAG,KAAK;QACzBD,OAAO,CAAC/V,SAAS,GACf,CAAC,IAAI,CAAClB,YAAY,CAACsB,WAAW,CAACyI,eAAe,CAACC,QAAQ;QAEzD;QACAiN,OAAO,CAACE,YAAY,GAAGN,eAAe;QAEtC;QACAI,OAAO,CAACnP,aAAa,GAAGA,aAAa;QAErC;QACAmP,OAAO,CAACG,MAAM,GAAG,IAAI,CAACnZ,CAAC,CAACzB,EAAE,CAAC6a,UAAU,EAAE;UACrCC,cAAc,EAAE,CAAC;UACjB;UACAlS,SAAS,EAAE5I,EAAE,CAAC+a,aAAa,CAACC,QAAQ,CAAE;SACvC,CAAC;QAEF;QACA,MAAM9W,KAAK,GAAG,IAAIlE,EAAE,CAACib,eAAe,CAACX,MAAM,CAAC5X,IAAI,EAAE,EAAE,CAAC;QACrD,IAAI4X,MAAM,CAACY,KAAK,EAAE;UAChBhX,KAAK,CAACiX,qBAAqB,CAACb,MAAM,CAACY,KAAK,CAAC;;QAE3CT,OAAO,CAACvW,KAAK,GAAGA,KAAK;;IAEzB,CAAC,CAAC;EACJ;EAAC,QAAAkX,CAAA,G;qBAnxDU5a,WAAW,EAAA6a,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,sBAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,kBAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,iBAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,oBAAA,GAAAnB,EAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAAC,sBAAA,GAAArB,EAAA,CAAAC,QAAA,CAAAqB,GAAA,CAAAC,iBAAA,GAAAvB,EAAA,CAAAC,QAAA,CAAAuB,GAAA,CAAAC,kBAAA,GAAAzB,EAAA,CAAAC,QAAA,CAAAyB,GAAA,CAAAC,eAAA,GAAA3B,EAAA,CAAAC,QAAA,CAAA2B,GAAA,CAAAC,eAAA,GAAA7B,EAAA,CAAAC,QAAA,CAAA6B,GAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAX7c,WAAW;IAAA8c,OAAA,EAAX9c,WAAW,CAAA+c,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}