/* Math Editor Dialog Styles - Enhanced Microsoft Word Design */
.matheditor-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    backdrop-filter: blur(2px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.matheditor-content {
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
    border-radius: 8px;
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    width: 75%;
    max-width: 900px;
    max-height: 85vh;
    overflow: hidden;
    position: relative;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.matheditor-container {
    padding: 0 16px 16px 16px;
    overflow-y: auto;
    max-height: calc(85vh - 60px);
}

.matheditor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: 1px solid #e1e5e9;
    padding: 0 20px;
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    color: #323130;
    height: 48px;
    border-radius: 8px 8px 0 0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.matheditor-header h2 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    color: #323130;
    letter-spacing: -0.01em;
}

.matheditor-close-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #605e5c;
    padding: 6px;
    line-height: 1;
    border-radius: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
    position: relative;
}

.matheditor-close-btn:hover {
    background-color: #f3f2f1;
    color: #323130;
    transform: scale(1.05);
}

.matheditor-close-btn:active {
    background-color: #edebe9;
    transform: scale(0.98);
}

/* Enhanced Input Container */
.matheditor-input-container {
    margin-bottom: 16px;
    margin-top: 8px;
    padding: 0;
}

.matheditor-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #323130;
    font-size: 12px;
    letter-spacing: -0.01em;
}

#matheditor-input {
    width: 100%;
    font-size: 16px;
    border: 2px solid #d1d1d1;
    border-radius: 6px;
    min-height: 140px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 1px 2px rgba(0, 0, 0, 0.04);
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 8px;
    padding: 12px;
    box-sizing: border-box;
}

#matheditor-input:focus {
    border-color: #0078d4;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 0 0 2px rgba(0, 120, 212, 0.2), 0 2px 4px rgba(0, 120, 212, 0.1);
    background: #ffffff;
    outline: none;
}

#matheditor-input:hover {
    border-color: #a6c8e8;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 2px 4px rgba(0, 120, 212, 0.08);
}

/* Enhanced Toolbar Tabs - Microsoft Word Style */
.matheditor-toolbar-tabs {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
    border-bottom: 1px solid #d1d1d1;
    padding: 0 8px;
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    overflow-x: auto;
    overflow-y: hidden;
    height: 32px;
    position: relative;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.matheditor-toolbar-tabs::-webkit-scrollbar {
    height: 2px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-track {
    background: transparent;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #c7e0f4, #0078d4);
    border-radius: 2px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to right, #a6c8e8, #106ebe);
}

.matheditor-tab-btn {
    padding: 0 12px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 11px;
    font-weight: 400;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 1px;
    color: #605e5c;
    position: relative;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    border-radius: 4px 4px 0 0;
    min-width: fit-content;
}

.matheditor-tab-btn:hover {
    color: #323130;
    background: linear-gradient(to bottom, #f3f2f1, #e1dfdd);
    transform: translateY(-1px);
}

.matheditor-tab-btn:active {
    transform: translateY(0);
    background: linear-gradient(to bottom, #edebe9, #d2d0ce);
}

.matheditor-tab-btn.active {
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    color: #0078d4;
    font-weight: 600;
    border: 1px solid #d1d1d1;
    border-bottom: 1px solid #ffffff;
    margin-bottom: -1px;
    z-index: 3;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.matheditor-tab-btn.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #0078d4;
}

/* Enhanced Toolbar Content - Compact Microsoft Word Style */
.matheditor-toolbar {
    display: block;
    margin-bottom: 12px;
    padding: 8px 12px 20px 12px;
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
    border: 1px solid #d1d1d1;
    border-top: none;
    border-radius: 0 0 6px 6px;
    min-height: 72px;
    max-height: 120px;
    overflow-y: auto;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 2px 4px rgba(0, 0, 0, 0.04);
    position: relative;
    z-index: 1;
}

.matheditor-toolbar::-webkit-scrollbar {
    width: 6px;
}

.matheditor-toolbar::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.matheditor-toolbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #c7e0f4, #a6c8e8);
    border-radius: 3px;
    border: 1px solid #d1d1d1;
}

.matheditor-toolbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #a6c8e8, #85c1e9);
}

.matheditor-tab-content {
    display: none;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
    position: relative;
    min-height: 52px;
}

.matheditor-tab-content.active {
    display: flex;
}

/* Enhanced Button Groups - Compact Design */
.matheditor-button-group {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 2px;
    padding: 4px 8px 16px 4px;
    border-right: 1px solid #e1e5e9;
    position: relative;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.8),
        rgba(248, 249, 250, 0.4)
    );
    border-radius: 4px;
    transition: all 0.15s ease;
}

.matheditor-button-group:hover {
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.95),
        rgba(243, 242, 241, 0.6)
    );
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.matheditor-button-group:last-child {
    border-right: none;
}

/* Compact Group Labels */
.matheditor-group-label {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 9px;
    color: #605e5c;
    font-weight: 500;
    white-space: nowrap;
    text-align: center;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.9),
        rgba(248, 249, 250, 0.9)
    );
    padding: 1px 4px;
    border-radius: 2px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
}

/* Enhanced Compact Button Styling - Microsoft Word Style */
.matheditor-toolbar-btn {
    padding: 3px 4px;
    border: 1px solid transparent;
    border-radius: 3px;
    background: transparent;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    transition: all 0.12s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 24px;
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #323130;
    box-shadow: none;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

/* Enhanced icon styling */
.matheditor-toolbar-btn i {
    font-size: 12px;
    color: #323130;
    transition: all 0.12s ease;
    line-height: 1;
}

/* Compact text styling */
.matheditor-toolbar-btn .btn-text {
    margin-left: 3px;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: -0.01em;
}

/* Professional hover effect */
.matheditor-toolbar-btn:hover {
    background: linear-gradient(to bottom, #e5f3ff, #d1ebff);
    color: #0078d4;
    border-color: #c7e0f4;
    transform: translateY(-0.5px);
    box-shadow: 0 2px 4px rgba(0, 120, 212, 0.15);
}

.matheditor-toolbar-btn:hover i {
    color: #0078d4;
    transform: scale(1.05);
}

/* Active/pressed state */
.matheditor-toolbar-btn:active {
    background: linear-gradient(to bottom, #c7e0f4, #b3d6f2);
    color: #106ebe;
    border-color: #a6c8e8;
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 120, 212, 0.2);
}

.matheditor-toolbar-btn:active i {
    color: #106ebe;
    transform: scale(0.98);
}

/* Focus state for accessibility */
.matheditor-toolbar-btn:focus {
    outline: 2px solid #0078d4;
    outline-offset: 1px;
    z-index: 10;
}

/* Large button styling - more prominent */
.matheditor-toolbar-btn.large {
    padding: 4px 6px;
    font-size: 12px;
    min-width: 28px;
    height: 28px;
    font-weight: 500;
    border-radius: 4px;
}

.matheditor-toolbar-btn.large i {
    font-size: 14px;
}

.matheditor-toolbar-btn.large .btn-text {
    font-size: 11px;
    margin-left: 4px;
}

.matheditor-toolbar-btn.large:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 120, 212, 0.2);
}

/* Button loading state */
.matheditor-toolbar-btn.loading {
    opacity: 0.6;
    cursor: wait;
    pointer-events: none;
}

.matheditor-toolbar-btn.loading::after {
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    border: 2px solid #0078d4;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Enhanced Search Functionality */
.matheditor-search-container {
    margin-bottom: 8px;
    padding: 8px 12px;
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #d1d1d1;
}

.matheditor-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 1px solid #d1d1d1;
    border-radius: 4px;
    padding: 4px 8px;
    transition: all 0.15s ease;
}

.matheditor-search-wrapper:focus-within {
    border-color: #0078d4;
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.matheditor-search-icon {
    color: #605e5c;
    font-size: 12px;
    margin-right: 6px;
}

.matheditor-search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 12px;
    color: #323130;
    background: transparent;
    padding: 4px 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.matheditor-search-input::placeholder {
    color: #a19f9d;
    font-style: italic;
}

.matheditor-search-clear {
    background: none;
    border: none;
    color: #605e5c;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
    margin-left: 4px;
}

.matheditor-search-clear:hover {
    background: #f3f2f1;
    color: #323130;
}

.matheditor-search-clear i {
    font-size: 10px;
}

/* More Button for Overflow */
.matheditor-more-btn {
    padding: 4px 8px;
    border: 1px solid #d1d1d1;
    border-radius: 3px;
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    cursor: pointer;
    font-size: 11px;
    color: #605e5c;
    font-weight: 500;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
}

.matheditor-more-btn:hover {
    background: linear-gradient(to bottom, #e1dfdd, #d2d0ce);
    border-color: #a6a6a6;
    color: #323130;
}

.matheditor-more-btn i {
    font-size: 10px;
}

.matheditor-more-btn span {
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

/* High Priority Groups */
.matheditor-button-group.high-priority {
    order: -1;
    background: linear-gradient(
        to bottom,
        rgba(0, 120, 212, 0.05),
        rgba(0, 120, 212, 0.02)
    );
    border-color: rgba(0, 120, 212, 0.2);
}

/* Common Symbol Highlighting */
.matheditor-toolbar-btn.common-symbol {
    background: linear-gradient(
        to bottom,
        rgba(0, 120, 212, 0.03),
        rgba(0, 120, 212, 0.01)
    );
}

.matheditor-toolbar-btn.common-symbol:hover {
    background: linear-gradient(to bottom, #e5f3ff, #d1ebff);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 120, 212, 0.2);
}

/* Search Hidden State */
.search-hidden {
    display: none !important;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .matheditor-content {
        width: 95%;
        max-width: none;
        margin: 10px;
    }

    .matheditor-toolbar {
        max-height: 100px;
    }

    .matheditor-button-group {
        padding: 2px 4px 12px 2px;
        gap: 1px;
    }

    .matheditor-toolbar-btn {
        min-width: 20px;
        height: 20px;
        padding: 2px 3px;
    }

    .matheditor-toolbar-btn i {
        font-size: 10px;
    }

    .matheditor-group-label {
        font-size: 8px;
        bottom: 1px;
    }
}

/* No longer using category headers */

/* Enhanced Action Buttons Container */
.matheditor-buttons-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    padding: 12px 0 0;
    border-top: 1px solid #e1e5e9;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.8),
        rgba(248, 249, 250, 0.4)
    );
}

/* Professional Cancel Button */
.matheditor-cancel-btn {
    padding: 8px 16px;
    border: 1px solid #d1d1d1;
    border-radius: 4px;
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    cursor: pointer;
    font-size: 13px;
    color: #323130;
    font-weight: 500;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 80px;
}

.matheditor-cancel-btn:hover {
    background: linear-gradient(to bottom, #f3f2f1, #e1dfdd);
    border-color: #a6a6a6;
    color: #201f1e;
    transform: translateY(-0.5px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.matheditor-cancel-btn:active {
    background: linear-gradient(to bottom, #edebe9, #d2d0ce);
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.matheditor-cancel-btn:focus {
    outline: 2px solid #605e5c;
    outline-offset: 1px;
}

/* Professional Insert Button */
.matheditor-insert-btn {
    padding: 8px 20px;
    border: 1px solid #0078d4;
    border-radius: 4px;
    background: linear-gradient(to bottom, #0078d4, #106ebe);
    color: #ffffff;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 120, 212, 0.25);
    position: relative;
    overflow: hidden;
    min-width: 90px;
}

.matheditor-insert-btn:hover {
    background: linear-gradient(to bottom, #106ebe, #005a9e);
    border-color: #005a9e;
    transform: translateY(-0.5px);
    box-shadow: 0 3px 6px rgba(0, 120, 212, 0.35);
}

.matheditor-insert-btn:active {
    background: linear-gradient(to bottom, #005a9e, #004578);
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.matheditor-insert-btn:focus {
    outline: 2px solid #ffffff;
    outline-offset: 1px;
}

/* Button loading states */
.matheditor-insert-btn.loading,
.matheditor-cancel-btn.loading {
    opacity: 0.7;
    cursor: wait;
    pointer-events: none;
}

.matheditor-insert-btn.loading::after {
    content: "";
    position: absolute;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

/* Shine effect on insert button */
.matheditor-insert-btn::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    transition: transform 0.5s;
    opacity: 0;
}

.matheditor-insert-btn:hover::after {
    opacity: 1;
    transform: rotate(30deg) translate(50%, -50%);
    transition: transform 0.7s, opacity 0.5s;
}

/* Hide MathLive virtual keyboard toggle button */
.ML__virtual-keyboard-toggle,
[part="virtual-keyboard-toggle"],
[data-ml__tooltip="Toggle Virtual Keyboard"],
[data-command="&quot;toggleVirtualKeyboard&quot;"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

math-field::part(virtual-keyboard-toggle) {
    display: none !important;
}
