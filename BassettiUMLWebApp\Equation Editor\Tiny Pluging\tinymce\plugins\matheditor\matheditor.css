/* Math Editor Dialog Styles - Enhanced Microsoft Word Design */
.matheditor-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.45),
        rgba(0, 0, 0, 0.65)
    );
    backdrop-filter: blur(3px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto",
        "Helvetica Neue", sans-serif;
    animation: fadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(3px);
    }
}

.matheditor-content {
    background: linear-gradient(
        to bottom,
        #ffffff 0%,
        #fafbfc 50%,
        #f8f9fa 100%
    );
    border-radius: 10px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.18), 0 8px 24px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    width: 78%;
    max-width: 920px;
    max-height: 88vh;
    overflow: hidden;
    position: relative;
    animation: slideIn 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-origin: center center;
}

@keyframes slideIn {
    from {
        transform: translateY(-30px) scale(0.92);
        opacity: 0;
        filter: blur(1px);
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
        filter: blur(0px);
    }
}

.matheditor-container {
    padding: 0 14px 14px 14px;
    overflow-y: auto;
    max-height: calc(88vh - 56px);
    scrollbar-width: thin;
    scrollbar-color: #c7e0f4 #f8f9fa;
}

.matheditor-container::-webkit-scrollbar {
    width: 8px;
}

.matheditor-container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.matheditor-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #c7e0f4, #a6c8e8);
    border-radius: 4px;
    border: 1px solid #d1d1d1;
}

.matheditor-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #a6c8e8, #85c1e9);
}

.matheditor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: 1px solid #e1e5e9;
    padding: 0 18px;
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    color: #323130;
    height: 44px;
    border-radius: 10px 10px 0 0;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.matheditor-header h2 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #323130;
    letter-spacing: -0.02em;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.matheditor-close-btn {
    background: none;
    border: none;
    font-size: 15px;
    cursor: pointer;
    color: #605e5c;
    padding: 5px;
    line-height: 1;
    border-radius: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.matheditor-close-btn::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(
        circle,
        rgba(0, 120, 212, 0.1) 0%,
        transparent 70%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: -1;
}

.matheditor-close-btn:hover {
    background-color: #f3f2f1;
    color: #323130;
    transform: scale(1.08);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.matheditor-close-btn:hover::before {
    width: 40px;
    height: 40px;
}

.matheditor-close-btn:active {
    background-color: #edebe9;
    transform: scale(0.95);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}

/* Enhanced Input Container */
.matheditor-input-container {
    margin-bottom: 16px;
    margin-top: 8px;
    padding: 0;
}

.matheditor-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #323130;
    font-size: 12px;
    letter-spacing: -0.01em;
}

#matheditor-input {
    width: 100%;
    font-size: 16px;
    border: 2px solid #d1d1d1;
    border-radius: 6px;
    min-height: 140px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 1px 2px rgba(0, 0, 0, 0.04);
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 8px;
    padding: 12px;
    box-sizing: border-box;
}

#matheditor-input:focus {
    border-color: #0078d4;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 0 0 2px rgba(0, 120, 212, 0.2), 0 2px 4px rgba(0, 120, 212, 0.1);
    background: #ffffff;
    outline: none;
}

#matheditor-input:hover {
    border-color: #a6c8e8;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06),
        0 2px 4px rgba(0, 120, 212, 0.08);
}

/* Enhanced Toolbar Tabs - Microsoft Word Style */
.matheditor-toolbar-tabs {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
    border-bottom: 1px solid #d1d1d1;
    padding: 0 6px;
    background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    overflow-x: auto;
    overflow-y: hidden;
    height: 30px;
    position: relative;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
    scrollbar-width: thin;
    scrollbar-color: #c7e0f4 transparent;
}

.matheditor-toolbar-tabs::-webkit-scrollbar {
    height: 3px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-track {
    background: transparent;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #c7e0f4, #0078d4);
    border-radius: 3px;
    transition: all 0.2s ease;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to right, #a6c8e8, #106ebe);
}

.matheditor-tab-btn {
    padding: 0 10px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 10px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 0.5px;
    color: #605e5c;
    position: relative;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    border-radius: 5px 5px 0 0;
    min-width: fit-content;
    letter-spacing: -0.01em;
    text-transform: uppercase;
    overflow: hidden;
}

.matheditor-tab-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    opacity: 0;
    transition: opacity 0.2s ease;
}

.matheditor-tab-btn:hover {
    color: #323130;
    background: linear-gradient(to bottom, #f3f2f1 0%, #e1dfdd 100%);
    transform: translateY(-0.5px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.matheditor-tab-btn:hover::before {
    opacity: 1;
}

.matheditor-tab-btn:active {
    transform: translateY(0);
    background: linear-gradient(to bottom, #edebe9, #d2d0ce);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.matheditor-tab-btn.active {
    background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    color: #0078d4;
    font-weight: 600;
    border: 1px solid #d1d1d1;
    border-bottom: 1px solid #ffffff;
    margin-bottom: -1px;
    z-index: 3;
    box-shadow: 0 -3px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.matheditor-tab-btn.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, #0078d4, #106ebe);
    border-radius: 2px 2px 0 0;
    box-shadow: 0 1px 3px rgba(0, 120, 212, 0.3);
}

/* Enhanced Toolbar Content - Compact Microsoft Word Style */
.matheditor-toolbar {
    display: block;
    margin-bottom: 10px;
    padding: 6px 10px 16px 10px;
    background: linear-gradient(
        to bottom,
        #ffffff 0%,
        #fafbfc 50%,
        #f8f9fa 100%
    );
    border: 1px solid #d1d1d1;
    border-top: none;
    border-radius: 0 0 8px 8px;
    min-height: 64px;
    max-height: 110px;
    overflow-y: auto;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.9),
        0 2px 6px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.04);
    position: relative;
    z-index: 1;
    scrollbar-width: thin;
    scrollbar-color: #c7e0f4 #f8f9fa;
}

.matheditor-toolbar::-webkit-scrollbar {
    width: 5px;
}

.matheditor-toolbar::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.matheditor-toolbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #c7e0f4, #a6c8e8);
    border-radius: 3px;
    border: 1px solid rgba(209, 209, 209, 0.6);
    transition: all 0.2s ease;
}

.matheditor-toolbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #a6c8e8, #85c1e9);
    border-color: #a6c8e8;
}

.matheditor-tab-content {
    display: none;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 6px;
    position: relative;
    min-height: 48px;
    animation: fadeInContent 0.3s ease-out;
}

.matheditor-tab-content.active {
    display: flex;
}

@keyframes fadeInContent {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Button Groups - Compact Design */
.matheditor-button-group {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 1.5px;
    padding: 3px 6px 14px 3px;
    border-right: 1px solid rgba(225, 229, 233, 0.7);
    position: relative;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.85),
        rgba(248, 249, 250, 0.5)
    );
    border-radius: 5px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.matheditor-button-group::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 120, 212, 0.02) 0%,
        transparent 50%,
        rgba(0, 120, 212, 0.01) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.matheditor-button-group:hover {
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.98),
        rgba(243, 242, 241, 0.7)
    );
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-0.5px);
}

.matheditor-button-group:hover::before {
    opacity: 1;
}

.matheditor-button-group:last-child {
    border-right: none;
}

/* Compact Group Labels */
.matheditor-group-label {
    position: absolute;
    bottom: 1px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    color: #605e5c;
    font-weight: 600;
    white-space: nowrap;
    text-align: center;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.95),
        rgba(248, 249, 250, 0.95)
    );
    padding: 1px 3px;
    border-radius: 3px;
    letter-spacing: 0.03em;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(225, 229, 233, 0.3);
}

/* Enhanced Compact Button Styling - Microsoft Word Style */
.matheditor-toolbar-btn {
    padding: 2px 3px;
    border: 1px solid transparent;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 22px;
    height: 22px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #323130;
    box-shadow: none;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(0px);
}

.matheditor-toolbar-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at center,
        rgba(0, 120, 212, 0.08) 0%,
        transparent 70%
    );
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced icon styling */
.matheditor-toolbar-btn i {
    font-size: 11px;
    color: #323130;
    transition: all 0.15s ease;
    line-height: 1;
    z-index: 1;
}

/* Compact text styling */
.matheditor-toolbar-btn .btn-text {
    margin-left: 2px;
    font-size: 9px;
    font-weight: 600;
    letter-spacing: -0.02em;
    z-index: 1;
}

/* Professional hover effect */
.matheditor-toolbar-btn:hover {
    background: linear-gradient(to bottom, #e5f3ff 0%, #d1ebff 100%);
    color: #0078d4;
    border-color: rgba(199, 224, 244, 0.8);
    transform: translateY(-0.8px);
    box-shadow: 0 3px 8px rgba(0, 120, 212, 0.18);
    backdrop-filter: blur(1px);
}

.matheditor-toolbar-btn:hover::before {
    opacity: 1;
    transform: scale(1.2);
}

.matheditor-toolbar-btn:hover i {
    color: #0078d4;
    transform: scale(1.08);
}

/* Active/pressed state */
.matheditor-toolbar-btn:active {
    background: linear-gradient(to bottom, #c7e0f4, #b3d6f2);
    color: #106ebe;
    border-color: #a6c8e8;
    transform: translateY(-0.2px);
    box-shadow: inset 0 1px 3px rgba(0, 120, 212, 0.25);
}

.matheditor-toolbar-btn:active::before {
    opacity: 0.5;
    transform: scale(0.9);
}

.matheditor-toolbar-btn:active i {
    color: #106ebe;
    transform: scale(0.95);
}

/* Focus state for accessibility */
.matheditor-toolbar-btn:focus {
    outline: 2px solid #0078d4;
    outline-offset: 2px;
    z-index: 10;
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
}

/* Large button styling - more prominent */
.matheditor-toolbar-btn.large {
    padding: 3px 5px;
    font-size: 11px;
    min-width: 26px;
    height: 26px;
    font-weight: 600;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.matheditor-toolbar-btn.large i {
    font-size: 13px;
}

.matheditor-toolbar-btn.large .btn-text {
    font-size: 10px;
    margin-left: 3px;
}

.matheditor-toolbar-btn.large:hover {
    transform: translateY(-1.2px);
    box-shadow: 0 4px 12px rgba(0, 120, 212, 0.25);
}

/* Button loading state */
.matheditor-toolbar-btn.loading {
    opacity: 0.65;
    cursor: wait;
    pointer-events: none;
    animation: pulse 1.5s ease-in-out infinite;
}

.matheditor-toolbar-btn.loading::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    border: 2px solid #0078d4;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    z-index: 2;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 0.65;
    }
    50% {
        opacity: 0.85;
    }
}

/* Enhanced Search Functionality */
.matheditor-search-container {
    margin-bottom: 6px;
    padding: 6px 10px;
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(209, 209, 209, 0.8);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.matheditor-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
    border: 1px solid #d1d1d1;
    border-radius: 6px;
    padding: 3px 6px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.04);
}

.matheditor-search-wrapper:focus-within {
    border-color: #0078d4;
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.15),
        inset 0 1px 2px rgba(0, 0, 0, 0.04);
    background: #ffffff;
}

.matheditor-search-icon {
    color: #605e5c;
    font-size: 11px;
    margin-right: 5px;
    transition: color 0.2s ease;
}

.matheditor-search-wrapper:focus-within .matheditor-search-icon {
    color: #0078d4;
}

.matheditor-search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 11px;
    color: #323130;
    background: transparent;
    padding: 3px 0;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto",
        "Helvetica Neue", sans-serif;
    font-weight: 500;
}

.matheditor-search-input::placeholder {
    color: #a19f9d;
    font-style: italic;
    font-weight: 400;
}

.matheditor-search-clear {
    background: none;
    border: none;
    color: #605e5c;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-left: 3px;
    width: 16px;
    height: 16px;
}

.matheditor-search-clear:hover {
    background: #f3f2f1;
    color: #323130;
    transform: scale(1.1);
}

.matheditor-search-clear i {
    font-size: 9px;
}

/* More Button for Overflow */
.matheditor-more-btn {
    padding: 3px 6px;
    border: 1px solid rgba(209, 209, 209, 0.8);
    border-radius: 5px;
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    cursor: pointer;
    font-size: 10px;
    color: #605e5c;
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 3px;
    margin-top: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.matheditor-more-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    transition: left 0.5s ease;
}

.matheditor-more-btn:hover {
    background: linear-gradient(to bottom, #e1dfdd 0%, #d2d0ce 100%);
    border-color: #a6a6a6;
    color: #323130;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.matheditor-more-btn:hover::before {
    left: 100%;
}

.matheditor-more-btn i {
    font-size: 9px;
    transition: transform 0.2s ease;
}

.matheditor-more-btn:hover i {
    transform: scale(1.1);
}

.matheditor-more-btn span {
    font-size: 9px;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.matheditor-more-btn.expanded {
    background: linear-gradient(to bottom, #0078d4, #106ebe);
    color: #ffffff;
    border-color: #0078d4;
}

.matheditor-more-btn.expanded:hover {
    background: linear-gradient(to bottom, #106ebe, #005a9e);
}

/* High Priority Groups */
.matheditor-button-group.high-priority {
    order: -1;
    background: linear-gradient(
        to bottom,
        rgba(0, 120, 212, 0.08),
        rgba(0, 120, 212, 0.03)
    );
    border-color: rgba(0, 120, 212, 0.25);
    box-shadow: 0 1px 4px rgba(0, 120, 212, 0.1);
}

/* Common Symbol Highlighting */
.matheditor-toolbar-btn.common-symbol {
    background: linear-gradient(
        to bottom,
        rgba(0, 120, 212, 0.05),
        rgba(0, 120, 212, 0.02)
    );
    border-color: rgba(0, 120, 212, 0.1);
}

.matheditor-toolbar-btn.common-symbol:hover {
    background: linear-gradient(to bottom, #e5f3ff, #d1ebff);
    transform: translateY(-1.2px);
    box-shadow: 0 4px 10px rgba(0, 120, 212, 0.25);
}

/* Search Hidden State */
.search-hidden {
    display: none !important;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .matheditor-content {
        width: 96%;
        max-width: none;
        margin: 8px;
        border-radius: 8px;
    }

    .matheditor-toolbar {
        max-height: 90px;
        padding: 4px 8px 12px 8px;
    }

    .matheditor-button-group {
        padding: 2px 4px 10px 2px;
        gap: 1px;
    }

    .matheditor-toolbar-btn {
        min-width: 18px;
        height: 18px;
        padding: 1px 2px;
        font-size: 10px;
    }

    .matheditor-toolbar-btn i {
        font-size: 9px;
    }

    .matheditor-group-label {
        font-size: 7px;
        bottom: 0px;
        padding: 0px 2px;
    }

    .matheditor-tab-btn {
        padding: 0 8px;
        font-size: 9px;
        height: 28px;
    }

    .matheditor-search-container {
        padding: 4px 8px;
    }

    .matheditor-search-wrapper {
        padding: 2px 4px;
    }

    .matheditor-search-input {
        font-size: 10px;
    }
}

/* No longer using category headers */

/* Enhanced Action Buttons Container */
.matheditor-buttons-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    padding: 10px 0 0;
    border-top: 1px solid rgba(225, 229, 233, 0.8);
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.9),
        rgba(248, 249, 250, 0.5)
    );
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.04);
}

/* Professional Cancel Button */
.matheditor-cancel-btn {
    padding: 7px 14px;
    border: 1px solid rgba(209, 209, 209, 0.8);
    border-radius: 6px;
    background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    cursor: pointer;
    font-size: 12px;
    color: #323130;
    font-weight: 600;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto",
        "Helvetica Neue", sans-serif;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 75px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.matheditor-cancel-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s ease;
}

.matheditor-cancel-btn:hover {
    background: linear-gradient(to bottom, #f3f2f1 0%, #e1dfdd 100%);
    border-color: #a6a6a6;
    color: #201f1e;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.matheditor-cancel-btn:hover::before {
    left: 100%;
}

.matheditor-cancel-btn:active {
    background: linear-gradient(to bottom, #edebe9, #d2d0ce);
    transform: translateY(0);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
}

.matheditor-cancel-btn:focus {
    outline: 2px solid #605e5c;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(96, 94, 92, 0.2);
}

/* Professional Insert Button */
.matheditor-insert-btn {
    padding: 7px 18px;
    border: 1px solid #0078d4;
    border-radius: 6px;
    background: linear-gradient(to bottom, #0078d4 0%, #106ebe 100%);
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    font-weight: 700;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto",
        "Helvetica Neue", sans-serif;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 8px rgba(0, 120, 212, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 85px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.matheditor-insert-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.25),
        transparent
    );
    transition: left 0.6s ease;
}

.matheditor-insert-btn:hover {
    background: linear-gradient(to bottom, #106ebe 0%, #005a9e 100%);
    border-color: #005a9e;
    transform: translateY(-1.5px);
    box-shadow: 0 5px 15px rgba(0, 120, 212, 0.4);
}

.matheditor-insert-btn:hover::before {
    left: 100%;
}

.matheditor-insert-btn:active {
    background: linear-gradient(to bottom, #005a9e, #004578);
    transform: translateY(-0.5px);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.25);
}

.matheditor-insert-btn:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.3);
}

/* Button loading states */
.matheditor-insert-btn.loading,
.matheditor-cancel-btn.loading {
    opacity: 0.7;
    cursor: wait;
    pointer-events: none;
}

.matheditor-insert-btn.loading::after {
    content: "";
    position: absolute;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

/* Shine effect on insert button */
.matheditor-insert-btn::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    transition: transform 0.5s;
    opacity: 0;
}

.matheditor-insert-btn:hover::after {
    opacity: 1;
    transform: rotate(30deg) translate(50%, -50%);
    transition: transform 0.7s, opacity 0.5s;
}

/* Hide MathLive virtual keyboard toggle button */
.ML__virtual-keyboard-toggle,
[part="virtual-keyboard-toggle"],
[data-ml__tooltip="Toggle Virtual Keyboard"],
[data-command="&quot;toggleVirtualKeyboard&quot;"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

math-field::part(virtual-keyboard-toggle) {
    display: none !important;
}

/* Enhanced Micro-Interactions and Animations */
.matheditor-toolbar-btn {
    will-change: transform, box-shadow;
}

.matheditor-toolbar-btn:hover {
    animation: buttonHover 0.3s ease-out;
}

@keyframes buttonHover {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-1px);
    }
    100% {
        transform: translateY(-0.8px);
    }
}

.matheditor-button-group {
    will-change: transform, box-shadow;
}

.matheditor-button-group:hover {
    animation: groupHover 0.4s ease-out;
}

@keyframes groupHover {
    0% {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    }
    100% {
        transform: translateY(-0.5px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
}

/* Tab switching animation */
.matheditor-tab-btn {
    will-change: transform, background-color;
}

.matheditor-tab-btn:not(.active):hover {
    animation: tabHover 0.2s ease-out;
}

@keyframes tabHover {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-0.5px);
    }
}

/* Enhanced focus indicators */
.matheditor-toolbar-btn:focus-visible {
    animation: focusPulse 2s ease-in-out infinite;
}

@keyframes focusPulse {
    0%, 100% {
        box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.4);
    }
}

/* Smooth transitions for all interactive elements */
* {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced loading states */
.matheditor-toolbar-btn.loading {
    animation: loadingPulse 1s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% {
        opacity: 0.65;
        transform: scale(1);
    }
    50% {
        opacity: 0.85;
        transform: scale(1.02);
    }
}

/* Staggered animation for button groups */
.matheditor-tab-content.active .matheditor-button-group {
    animation: staggerIn 0.4s ease-out;
}

.matheditor-tab-content.active .matheditor-button-group:nth-child(1) {
    animation-delay: 0ms;
}

.matheditor-tab-content.active .matheditor-button-group:nth-child(2) {
    animation-delay: 50ms;
}

.matheditor-tab-content.active .matheditor-button-group:nth-child(3) {
    animation-delay: 100ms;
}

.matheditor-tab-content.active .matheditor-button-group:nth-child(4) {
    animation-delay: 150ms;
}

.matheditor-tab-content.active .matheditor-button-group:nth-child(5) {
    animation-delay: 200ms;
}

@keyframes staggerIn {
    0% {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
