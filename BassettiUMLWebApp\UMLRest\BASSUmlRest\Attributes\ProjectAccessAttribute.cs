using BASSUmlBusiness.Extentions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace BASSUmlRest.Attributes
{
    /// <summary>
    /// Authorization attribute that validates user access to a specific project
    /// </summary>
    public class ProjectAccessAttribute : ActionFilterAttribute
    {
        private readonly AccessType _requiredAccessType;
        private readonly string _projectIdParameterName;

        /// <summary>
        /// Initializes a new instance of the ProjectAccessAttribute
        /// </summary>
        /// <param name="requiredAccessType">The minimum access type required</param>
        /// <param name="projectIdParameterName">The name of the parameter containing the project ID (default: "idProject")</param>
        public ProjectAccessAttribute(AccessType requiredAccessType, string projectIdParameterName = "idProject")
        {
            _requiredAccessType = requiredAccessType;
            _projectIdParameterName = projectIdParameterName;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            // Get the permission service from DI container
            var permissionService = context.HttpContext.RequestServices.GetService<IPermissionService>();
            if (permissionService == null)
            {
                context.Result = new StatusCodeResult(500);
                return;
            }

            // Extract project ID from route parameters or action parameters
            if (!TryGetProjectId(context, out int projectId))
            {
                context.Result = new BadRequestObjectResult(new { error = "Project ID is required" });
                return;
            }

            // Get user email from claims
            string? userEmail = context.HttpContext.User.GetEmployeeEmail() ?? context.HttpContext.User.GetEmail();
            if (string.IsNullOrEmpty(userEmail))
            {
                context.Result = new UnauthorizedObjectResult(new { error = "User not authenticated" });
                return;
            }

            try
            {
                // Check user permission for the project
                var permission = permissionService.GetProjectPermissionByUser(projectId, userEmail);
                
                // Validate access level
                if (!HasRequiredAccess(permission.AccessType, _requiredAccessType))
                {
                    context.Result = new ForbidResult();
                    return;
                }
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use a proper logging framework)
                Console.WriteLine($"Error checking project access: {ex.Message}");
                
                // Return appropriate error based on exception type
                if (ex.Message.Contains("not found") || ex.Message.Contains("NotFound"))
                {
                    context.Result = new NotFoundObjectResult(new { error = "Project not found or access denied" });
                }
                else
                {
                    context.Result = new StatusCodeResult(500);
                }
                return;
            }

            base.OnActionExecuting(context);
        }

        /// <summary>
        /// Attempts to extract the project ID from the action context
        /// </summary>
        private bool TryGetProjectId(ActionExecutingContext context, out int projectId)
        {
            projectId = 0;

            // First try to get from route values
            if (context.RouteData.Values.TryGetValue(_projectIdParameterName, out var routeValue))
            {
                if (int.TryParse(routeValue?.ToString(), out projectId))
                {
                    return true;
                }
            }

            // Then try to get from action parameters
            if (context.ActionArguments.TryGetValue(_projectIdParameterName, out var paramValue))
            {
                if (int.TryParse(paramValue?.ToString(), out projectId))
                {
                    return true;
                }
            }

            // Try alternative parameter names
            string[] alternativeNames = { "id", "projectId", "idProject" };
            foreach (var name in alternativeNames)
            {
                if (context.RouteData.Values.TryGetValue(name, out var altRouteValue))
                {
                    if (int.TryParse(altRouteValue?.ToString(), out projectId))
                    {
                        return true;
                    }
                }

                if (context.ActionArguments.TryGetValue(name, out var altParamValue))
                {
                    if (int.TryParse(altParamValue?.ToString(), out projectId))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if the user's access type meets the required access level
        /// </summary>
        private static bool HasRequiredAccess(AccessType userAccess, AccessType requiredAccess)
        {
            // Access hierarchy: Admin (0) > Edit (1) > View (2)
            // Lower numeric values have higher access
            return (int)userAccess <= (int)requiredAccess;
        }
    }

    /// <summary>
    /// Convenience attributes for common access levels
    /// </summary>
    public class RequireViewAccessAttribute : ProjectAccessAttribute
    {
        public RequireViewAccessAttribute(string projectIdParameterName = "idProject") 
            : base(AccessType.View, projectIdParameterName) { }
    }

    public class RequireEditAccessAttribute : ProjectAccessAttribute
    {
        public RequireEditAccessAttribute(string projectIdParameterName = "idProject") 
            : base(AccessType.Edit, projectIdParameterName) { }
    }

    public class RequireAdminAccessAttribute : ProjectAccessAttribute
    {
        public RequireAdminAccessAttribute(string projectIdParameterName = "idProject") 
            : base(AccessType.Admin, projectIdParameterName) { }
    }
}
